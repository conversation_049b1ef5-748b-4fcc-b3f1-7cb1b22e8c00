# OpenDataAI No-Code ML Platform 2.0

A comprehensive, object-oriented machine learning platform built with Streamlit and AutoGluon, designed for both beginners and experts to build, train, and deploy ML models without writing code.

## 🚀 Features

### Core Capabilities
- **No-Code Interface**: Intuitive Streamlit-based web interface
- **AutoML Integration**: Powered by AutoGluon for state-of-the-art automated machine learning
- **Multi-Modal Support**: Handle tabular, text, and image data in a single workflow
- **End-to-End Pipeline**: From data ingestion to model deployment

### Data Processing
- **Multiple Format Support**: CSV, JSON, Excel, Parquet, ZIP (for images)
- **Intelligent Preprocessing**: Automated feature engineering, encoding, and scaling
- **Data Validation**: Comprehensive data quality checks and validation
- **GPU Acceleration**: Optional RAPIDS/cuDF support for faster processing

### Machine Learning
- **AutoML Training**: Automated model selection and hyperparameter tuning
- **Problem Type Detection**: Automatic classification vs regression detection
- **Model Evaluation**: Comprehensive metrics, leaderboards, and visualizations
- **Ablation Studies**: Understand feature and component importance

### Advanced Features
- **Batch & Single Predictions**: Flexible prediction interfaces
- **Model Export**: Download trained models and predictions
- **API Server**: Built-in FastAPI server for model serving
- **State Management**: Persistent session state across workflow steps

## 🏗️ Architecture

The platform follows clean architecture principles with clear separation of concerns:

```
src/
├── models/          # Data models and configuration
├── data/           # Data processing and validation
├── ml/             # Machine learning components
├── ui/             # Streamlit user interface
└── main.py         # Application entry point
```

### Key Components

- **StateManager**: Centralized state management for Streamlit sessions
- **DataIngestionManager**: Handle multiple data sources and formats
- **DataPreprocessor**: Automated data preprocessing and feature engineering
- **ModelTrainer**: AutoGluon-based model training with configuration
- **ModelEvaluator**: Comprehensive model evaluation and metrics
- **ModelPredictor**: Flexible prediction interface for trained models
- **AblationAnalyzer**: Feature and component importance analysis

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- 4GB+ RAM recommended
- Optional: NVIDIA GPU for acceleration

### Quick Start

1. **Clone the repository:**
```bash
git clone https://github.com/opendataai/no-code-ml-platform.git
cd no-code-ml-platform
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Run the application:**
```bash
streamlit run app.py
```

### Development Installation

For development with additional tools:

```bash
pip install -e ".[dev]"
```

### GPU Support (Optional)

For RAPIDS/cuDF acceleration:

```bash
pip install -e ".[gpu]"
```

## 📖 Usage

### Basic Workflow

1. **Data Ingestion**: Upload your dataset or use demo data
2. **Exploration**: Analyze data distribution and quality
3. **Preprocessing**: Configure and apply data transformations
4. **Training**: Train models with AutoGluon
5. **Evaluation**: Analyze model performance and metrics
6. **Ablation**: Understand feature importance
7. **Prediction**: Make predictions on new data
8. **Export**: Download models and deploy via API

### Supported Data Types

- **Tabular Data**: CSV, Excel, Parquet files
- **Text Data**: JSON files with text columns
- **Image Data**: ZIP archives with image files
- **Mixed Data**: Combine tabular, text, and image features

### Demo Datasets

The platform includes several demo datasets:
- **California Housing**: Regression problem
- **Iris Flowers**: Multi-class classification
- **Wine Quality**: Classification with quality ratings
- **Breast Cancer**: Binary classification

## 🔧 Configuration

### Application Configuration

Modify `src/models/config.py` to customize:

```python
@dataclass
class AppConfig:
    app_title: str = "OpenDataAI — No-Code AI & Data Platform"
    max_preview_rows: int = 500
    enable_cudf: bool = True
    enable_multimodal: bool = True
```

### Model Configuration

Configure AutoGluon training parameters:

```python
@dataclass
class ModelConfig:
    preset: str = "medium_quality"
    time_limit: int = 300
    enable_multimodal: bool = False
```

### Preprocessing Configuration

Customize data preprocessing:

```python
@dataclass
class PreprocessingConfig:
    numeric_imputation: str = "mean"
    categorical_imputation: str = "mode"
    enable_onehot: bool = True
    enable_scaling: bool = False
```

## 🚀 API Server

The platform includes a built-in FastAPI server for model serving:

1. Train a model in the web interface
2. Go to the Export page
3. Click "Start API Server"
4. Send POST requests to `/predict` with CSV data

Example API usage:

```python
import requests
import pandas as pd

# Prepare data
data = pd.DataFrame({...})
csv_data = data.to_csv(index=False)

# Make prediction
response = requests.post(
    "http://localhost:8000/predict",
    files={"file": ("data.csv", csv_data, "text/csv")}
)

predictions = response.json()["predictions"]
```

## 🧪 Testing

Run the test suite:

```bash
pytest tests/
```

Run with coverage:

```bash
pytest --cov=src tests/
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Install development dependencies: `pip install -e ".[dev]"`
4. Make your changes
5. Run tests: `pytest`
6. Submit a pull request

### Code Style

We use:
- **Black** for code formatting
- **Flake8** for linting
- **MyPy** for type checking

Run all checks:

```bash
black src/
flake8 src/
mypy src/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **AutoGluon**: For providing excellent AutoML capabilities
- **Streamlit**: For the amazing web app framework
- **RAPIDS**: For GPU acceleration support
- **Plotly**: For interactive visualizations

## 📞 Support

- **Documentation**: [docs.opendataai.com](https://docs.opendataai.com)
- **Issues**: [GitHub Issues](https://github.com/opendataai/no-code-ml-platform/issues)
- **Discussions**: [GitHub Discussions](https://github.com/opendataai/no-code-ml-platform/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

- [ ] Advanced visualization dashboard
- [ ] Model versioning and experiment tracking
- [ ] Automated report generation
- [ ] Cloud deployment integration
- [ ] Real-time data streaming
- [ ] Advanced feature engineering
- [ ] Model interpretability tools
- [ ] A/B testing framework

---

**Built with ❤️ by the OpenDataAI Team**
