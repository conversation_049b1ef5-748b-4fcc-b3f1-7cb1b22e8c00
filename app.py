# app.py — OpenDataAI No-Code AI & Data Platform (Improved Single File)
import streamlit as st
import pandas as pd
import numpy as np
import os, io, tempfile, shutil, json, zipfile, subprocess, signal, sys, time, argparse
from typing import Optional
from autogluon.tabular import TabularPredictor
# Optional multimodal import; handle if not installed
try:
    from autogluon.multimodal import MultiModalPredictor
    _HAS_MULTIMODAL = True
except Exception:
    MultiModalPredictor = None
    _HAS_MULTIMODAL = False

# Optional RAPIDS/cuDF
_HAS_CUDF = False
try:
    import cudf
    _HAS_CUDF = True
except Exception:
    _HAS_CUDF = False

# ---------- Helpers ----------
def read_csv_optional_cudf(file) -> pd.DataFrame:
    """Read CSV (uploaded file or path). Prefer cudf if available; return pandas DataFrame."""
    try:
        if _HAS_CUDF:
            gdf = cudf.read_csv(file)
            return gdf.to_pandas()
    except Exception:
        pass
    return pd.read_csv(file)

def safe_preview(df: pd.DataFrame, max_rows: int = 200):
    """Return a preview DataFrame (sampled if too large) and a message about it."""
    if df is None:
        return None, "No data"
    try:
        n = len(df)
        if n > max_rows:
            return df.sample(n=max_rows, random_state=42), f"Preview sampling {max_rows} rows of {n} total"
        else:
            return df.head(max_rows), f"Previewing {n} rows"
    except Exception as e:
        return df.head(50), f"Preview limited due to error: {e}"

def ensure_dir(path: str):
    os.makedirs(path, exist_ok=True)

def cleanup_dir(path: str):
    if os.path.exists(path):
        shutil.rmtree(path)

def infer_problem_type(series: pd.Series) -> str:
    if pd.api.types.is_numeric_dtype(series):
        if series.nunique() > 20:
            return "regression"
        return "binary" if series.nunique() == 2 else "multiclass"
    return "multiclass"

# Quick train used by ablation; keeps runs short
def quick_train(df: pd.DataFrame, label: str, timeout: int = 30, preset: str = "medium_quality"):
    tmpdir = tempfile.mkdtemp(prefix="ag_tmp_")
    try:
        predictor = TabularPredictor(label=label, path=tmpdir)
        predictor.fit(train_data=df, presets=preset, time_limit=timeout, verbosity=0)
        try:
            perf = predictor.leaderboard(silent=True).iloc[0].to_dict()
        except Exception:
            perf = {}
        return perf
    finally:
        shutil.rmtree(tmpdir)

def run_ablation(df: pd.DataFrame, label: str, time_limit: int = 60):
    if hasattr(df, "to_pandas"):
        df = df.to_pandas()
    base_perf = quick_train(df, label, timeout=max(10, int(time_limit/3)))
    base_score = None
    for k, v in base_perf.items():
        if isinstance(v, (int, float)):
            base_score = v
            break
    cols = [c for c in df.columns if c != label]
    num_cols = [c for c in cols if pd.api.types.is_numeric_dtype(df[c])]
    cat_cols = [c for c in cols if not pd.api.types.is_numeric_dtype(df[c])]
    results = []
    for name, drop_cols in [("drop_numerical", num_cols), ("drop_categorical", cat_cols)]:
        if not drop_cols:
            results.append({"component": name, "delta": 0.0})
            continue
        df2 = df.drop(columns=drop_cols)
        perf = quick_train(df2, label, timeout=max(5, int(time_limit/6)))
        score = None
        for k, v in perf.items():
            if isinstance(v, (int, float)):
                score = v
                break
        delta = (base_score - score) if base_score is not None and score is not None else 0.0
        results.append({"component": name, "delta": float(delta)})
    return pd.DataFrame(sorted(results, key=lambda r: -abs(r["delta"])))

# ---------- Streamlit UI setup ----------
st.set_page_config("OpenDataAI — No-Code AI & Data Platform", layout="wide")
st.title("OpenDataAI — No-Code AI & Data Platform (AutoGluon 1.4) — Improved")

with st.sidebar:
    st.header("Navigation")
    page = st.radio("Go to", [
        "1. Data Ingestion",
        "2. Exploration",
        "3. Preprocessing",
        "4. Training",
        "5. Evaluation",
        "6. Ablation",
        "7. Prediction",
        "8. Export"
    ])
    if st.button("Reset All (clear session & models)"):
        cleanup_dir("models")
        for k in list(st.session_state.keys()):
            st.session_state.pop(k, None)
        st.rerun()
    st.write(f"RAPIDS/cuDF available: **{_HAS_CUDF}**")
    st.write(f"Multimodal module available: **{_HAS_MULTIMODAL}**")

# ---------- Step 1: Data Ingestion ----------
if page == "1. Data Ingestion":
    st.header("Step 1 — Data Ingestion")
    uploaded = st.file_uploader("Upload dataset (CSV / JSON / ZIP for images)", type=["csv", "json", "zip"])
    use_demo = st.checkbox("Use demo California housing dataset", value=False)
    if use_demo and not uploaded:
        from sklearn.datasets import fetch_california_housing
        data = fetch_california_housing(as_frame=True)
        df = data.frame
        st.session_state["data"] = df
        st.success("Loaded demo dataset.")
    elif uploaded is not None:
        filename = uploaded.name.lower()
        try:
            if filename.endswith(".csv"):
                df = read_csv_optional_cudf(uploaded)
                st.session_state["data"] = df
                st.success(f"Loaded CSV: {df.shape}")
            elif filename.endswith(".json"):
                df = pd.read_json(uploaded)
                st.session_state["data"] = df
                st.success(f"Loaded JSON: {df.shape}")
            elif filename.endswith(".zip"):
                tmpdir = tempfile.mkdtemp(prefix="images_")
                with zipfile.ZipFile(uploaded, "r") as z:
                    z.extractall(tmpdir)
                st.session_state["image_dir"] = tmpdir
                st.info(f"Extracted images to {tmpdir}. If using multimodal, map image filenames to a column in Step 3.")
        except Exception as e:
            st.error(f"Failed to load file: {e}")
    if "data" in st.session_state:
        preview, msg = safe_preview(st.session_state["data"], max_rows=500)
        st.write(msg)
        st.dataframe(preview)

# ---------- Step 2: Exploration ----------
if page == "2. Exploration":
    st.header("Step 2 — Exploration")
    if "data" not in st.session_state:
        st.info("Please upload data in Step 1.")
    else:
        df = st.session_state["data"]
        preview, msg = safe_preview(df, max_rows=500)
        st.write(msg)
        st.dataframe(preview)
        with st.expander("Column summary & types"):
            nulls = df.isnull().sum()
            dtypes = df.dtypes
            summary = pd.DataFrame({"dtype": dtypes.astype(str), "nulls": nulls, "unique": df.nunique()}).transpose().T
            # better formatting
            st.dataframe(summary)
        st.markdown("### Quick plots (select columns)")
        cols = list(df.columns)
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        cat_cols = [c for c in cols if c not in numeric_cols]
        c1 = st.selectbox("Numeric column for histogram", options=[""] + numeric_cols)
        if c1:
            try:
                st.bar_chart(df[c1].dropna().value_counts().head(50))
            except Exception as e:
                st.error(f"Plot error: {e}")
        c2 = st.selectbox("X (categorical) and numeric Y for aggregation", options=[""] + cat_cols)
        if c2 and numeric_cols:
            y = st.selectbox("Y column (numeric)", options=numeric_cols)
            if y:
                try:
                    agg = df.groupby(c2)[y].mean().sort_values(ascending=False).head(50)
                    st.bar_chart(agg)
                except Exception as e:
                    st.error(f"Aggregation error: {e}")

# ---------- Step 3: Preprocessing ----------
if page == "3. Preprocessing":
    st.header("Step 3 — Preprocessing & Feature Engineering")
    if "data" not in st.session_state:
        st.info("Please upload data first (Step 1).")
    else:
        df_orig = st.session_state["data"]
        preview, msg = safe_preview(df_orig, max_rows=200)
        st.write(msg)
        st.dataframe(preview)
        cols = list(df_orig.columns)

        st.markdown("### Select columns & quick actions")
        sel_cols = st.multiselect("Columns to keep", options=cols, default=cols)
        df = df_orig[sel_cols].copy()

        with st.expander("Column types & nulls"):
            st.dataframe(pd.DataFrame({"dtype": df.dtypes.astype(str), "nulls": df.isnull().sum(), "unique": df.nunique()}))

        st.markdown("### Imputation & encoding options")
        impute_numeric = st.selectbox("Numeric imputation", ["mean", "median", "constant"], index=0)
        impute_categorical = st.selectbox("Categorical imputation", ["mode", "constant"], index=0)
        fill_constant_numeric = st.number_input("Numeric constant (for 'constant')", value=0.0)
        fill_constant_cat = st.text_input("Categorical constant (for 'constant')", value="__MISSING__")

        do_onehot = st.checkbox("One-hot encode small categoricals (<=10)", value=True)
        do_label_encode = st.checkbox("Label encode large categoricals", value=False)
        do_scale_numeric = st.checkbox("Scale numeric features (StandardScaler)", value=False)
        add_date_parts = st.checkbox("Extract date parts from datetime columns", value=True)

        # Multimodal mapping UI: if images extracted but no image column, allow mapping
        if "image_dir" in st.session_state:
            st.markdown("### Image mapping (multimodal)")
            if "image" in df.columns:
                st.info("Dataset already contains 'image' column.")
            else:
                candidate_cols = [c for c in df.columns if df[c].dtype == object or pd.api.types.is_string_dtype(df[c])]
                st.write("Select column that contains image filenames (relative to the uploaded ZIP).")
                img_col_choice = st.selectbox("Image filename column", options=[""] + candidate_cols)
                if img_col_choice:
                    # create new 'image' column with full paths
                    image_dir = st.session_state["image_dir"]
                    def map_path(x):
                        p = os.path.join(image_dir, str(x))
                        return p if os.path.exists(p) else ""
                    df["image"] = df[img_col_choice].apply(map_path)
                    st.info("Created 'image' column mapping filenames to extracted image dir. Ensure filenames match exactly.")

        if st.button("Apply preprocessing"):
            df_proc = df.copy()
            # Numeric impute
            for c in df_proc.select_dtypes(include=[np.number]).columns:
                if df_proc[c].isnull().any():
                    if impute_numeric == "mean":
                        df_proc[c] = df_proc[c].fillna(df_proc[c].mean())
                    elif impute_numeric == "median":
                        df_proc[c] = df_proc[c].fillna(df_proc[c].median())
                    else:
                        df_proc[c] = df_proc[c].fillna(fill_constant_numeric)
            # Categorical impute
            for c in df_proc.select_dtypes(include=["object", "category"]).columns:
                if df_proc[c].isnull().any():
                    if impute_categorical == "mode":
                        mode_val = df_proc[c].mode().iloc[0] if not df_proc[c].mode().empty else fill_constant_cat
                        df_proc[c] = df_proc[c].fillna(mode_val)
                    else:
                        df_proc[c] = df_proc[c].fillna(fill_constant_cat)
            # Date parts
            if add_date_parts:
                for c in df_proc.columns:
                    try:
                        if pd.api.types.is_datetime64_any_dtype(df_proc[c]):
                            df_proc[f"{c}__year"] = df_proc[c].dt.year
                            df_proc[f"{c}__month"] = df_proc[c].dt.month
                            df_proc[f"{c}__day"] = df_proc[c].dt.day
                    except Exception:
                        pass
            # Encoding
            if do_onehot:
                for c in df_proc.select_dtypes(include=["object", "category"]).columns:
                    if df_proc[c].nunique() <= 10:
                        dummies = pd.get_dummies(df_proc[c], prefix=c, dummy_na=False)
                        df_proc = pd.concat([df_proc.drop(columns=[c]), dummies], axis=1)
            if do_label_encode:
                for c in df_proc.select_dtypes(include=["object", "category"]).columns:
                    if df_proc[c].nunique() > 10:
                        df_proc[c] = df_proc[c].astype("category").cat.codes
            # Scaling
            if do_scale_numeric:
                from sklearn.preprocessing import StandardScaler
                num_cols = df_proc.select_dtypes(include=[np.number]).columns.tolist()
                if num_cols:
                    scaler = StandardScaler()
                    df_proc[num_cols] = scaler.fit_transform(df_proc[num_cols])
            st.session_state["processed_data"] = df_proc
            st.success("Preprocessing applied. Stored in session_state['processed_data']")
            preview2, msg2 = safe_preview(df_proc, max_rows=200)
            st.write(msg2)
            st.dataframe(preview2)

# ---------- Step 4: Training ----------
if page == "4. Training":
    st.header("Step 4 — Training (AutoGluon)")
    if "processed_data" not in st.session_state and "data" not in st.session_state:
        st.info("Provide and preprocess data first.")
    else:
        df_use = st.session_state.get("processed_data", st.session_state.get("data"))
        preview, msg = safe_preview(df_use, max_rows=200)
        st.write(msg); st.dataframe(preview)
        cols = list(df_use.columns)
        default_target = cols[-1] if cols else None
        target_col = st.selectbox("Select target column", options=cols, index=cols.index(default_target) if default_target else 0)
        feature_cols = st.multiselect("Feature columns (empty = all except target)", options=[c for c in cols if c != target_col], default=[c for c in cols if c != target_col])
        if not feature_cols:
            feature_cols = [c for c in cols if c != target_col]
        st.markdown("### AutoGluon settings")
        ag_preset = st.selectbox("Preset", ["medium_quality","good_quality","best_quality","extreme_quality"], index=0)
        ag_time = st.number_input("Time limit (seconds)", min_value=30, value=300, step=30)
        use_multimodal = st.checkbox("Enable multimodal (if available & dataset has image/text columns)", value=False)
        multimodal_ready = False
        if use_multimodal and _HAS_MULTIMODAL:
            if "image" in df_use.columns or any(df_use.dtypes == object):
                multimodal_ready = True
        if use_multimodal and not _HAS_MULTIMODAL:
            st.warning("AutoGluon multimodal not installed/available in this environment.")
        if st.button("Train model"):
            ensure_dir("models")
            model_dir = os.path.join("models","ag_model")
            cleanup_dir(model_dir)
            os.makedirs(model_dir, exist_ok=True)
            df_train = df_use[feature_cols + [target_col]].copy()
            if hasattr(df_train, "to_pandas"):
                df_train = df_train.to_pandas()
            try:
                if use_multimodal and multimodal_ready:
                    st.info("Training MultiModalPredictor (may require GPU/more RAM).")
                    mm = MultiModalPredictor(label=target_col, path=model_dir)
                    with st.spinner("Fitting multimodal..."):
                        mm.fit(df_train, presets=ag_preset, time_limit=ag_time)
                    st.session_state["predictor_type"] = "multimodal"
                else:
                    st.info("Training Tabular predictor.")
                    tp = TabularPredictor(label=target_col, path=model_dir)
                    with st.spinner("Fitting tabular..."):
                        tp.fit(train_data=df_train, presets=ag_preset, time_limit=ag_time)
                    st.session_state["predictor_type"] = "tabular"
                st.session_state["predictor_path"] = model_dir
                st.success(f"Model trained and saved at {model_dir}")
            except Exception as e:
                st.error(f"Training failed: {e}")

# ---------- Step 5: Evaluation ----------
if page == "5. Evaluation":
    st.header("Step 5 — Evaluation & Diagnostics")
    if "predictor_path" not in st.session_state:
        st.info("Train a model first in Step 4.")
    else:
        ptype = st.session_state.get("predictor_type","tabular")
        model_dir = st.session_state["predictor_path"]
        st.write(f"Predictor: {ptype} at {model_dir}")
        try:
            if ptype == "multimodal":
                if not _HAS_MULTIMODAL:
                    st.error("Multimodal predictor not available in this environment.")
                else:
                    mp = MultiModalPredictor.load(model_dir)
                    try:
                        lb = mp.leaderboard(silent=True)
                        st.dataframe(lb)
                    except Exception:
                        st.info("Leaderboard not available for multimodal predictor.")
            else:
                tp = TabularPredictor.load(model_dir)
                try:
                    lb = tp.leaderboard(silent=True)
                    st.subheader("Leaderboard (top models)")
                    st.dataframe(lb.head(20))
                except Exception:
                    st.info("Leaderboard not available.")
                # Holdout evaluation if processed_data exists and contains label
                if "processed_data" in st.session_state and tp.label in st.session_state["processed_data"].columns:
                    df_eval = st.session_state["processed_data"]
                    holdout = df_eval.sample(frac=0.2, random_state=42)
                    X = holdout.drop(columns=[tp.label])
                    y = holdout[tp.label]
                    with st.spinner("Running holdout predictions..."):
                        preds = tp.predict(X)
                    from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
                    if pd.api.types.is_numeric_dtype(y):
                        mse = mean_squared_error(y, preds)
                        r2 = r2_score(y, preds)
                        st.write(f"Holdout MSE: **{mse:.4f}**, R2: **{r2:.4f}**")
                    else:
                        acc = accuracy_score(y, preds)
                        st.write(f"Holdout accuracy: **{acc:.4f}**")
                else:
                    st.info("No holdout evaluation: ensure processed_data has the label column.")
                # Feature importance (try/except)
                st.subheader("Feature importance (attempt)")
                try:
                    fi = tp.feature_importance(data=None)
                    if isinstance(fi, pd.DataFrame) and "importance" in fi.columns:
                        st.bar_chart(fi.sort_values("importance", ascending=False).head(20).set_index(fi.columns[0])["importance"])
                        st.dataframe(fi.sort_values("importance", ascending=False).head(50))
                    else:
                        st.dataframe(fi.head(50))
                except Exception:
                    st.info("Feature importance not available or too expensive to compute.")
        except Exception as e:
            st.error(f"Evaluation failed: {e}")

# ---------- Step 6: Ablation ----------
if page == "6. Ablation":
    st.header("Step 6 — Ablation & Component Impact (MLE-STAR inspired)")
    if "processed_data" not in st.session_state and "data" not in st.session_state:
        st.info("Provide data in Steps 1–3 first.")
    else:
        df_ab = st.session_state.get("processed_data", st.session_state.get("data"))
        cols = list(df_ab.columns)
        label_col = st.selectbox("Label column for ablation", options=cols, index=cols.index(cols[-1]) if cols else 0)
        ablation_time = st.number_input("Ablation total seconds", min_value=20, max_value=600, value=60, step=10)
        sample_frac = st.slider("Downsample fraction for ablation", min_value=0.1, max_value=1.0, value=0.5, step=0.1)
        if st.button("Run ablation analysis"):
            df_sample = df_ab.sample(frac=float(sample_frac), random_state=42)
            with st.spinner("Running ablation..."):
                try:
                    impacts_df = run_ablation(df_sample, label=label_col, time_limit=int(ablation_time))
                    st.success("Ablation complete")
                    st.dataframe(impacts_df)
                except Exception as e:
                    st.error(f"Ablation failed: {e}")

# ---------- Step 7: Prediction ----------
if page == "7. Prediction":
    st.header("Step 7 — Prediction (Batch & Single)")
    if "predictor_path" not in st.session_state:
        st.info("Train a model first in Step 4.")
    else:
        model_dir = st.session_state["predictor_path"]
        ptype = st.session_state.get("predictor_type","tabular")
        st.write(f"Using predictor at {model_dir} (type: {ptype})")
        pred_mode = st.radio("Mode", ["Batch CSV", "Single record", "Stream demo"])
        if pred_mode == "Batch CSV":
            uploaded_pred = st.file_uploader("Upload CSV to predict", type=["csv"])
            if uploaded_pred and st.button("Run batch prediction"):
                try:
                    if ptype == "multimodal":
                        if not _HAS_MULTIMODAL:
                            st.error("Multimodal not available.")
                        else:
                            mp = MultiModalPredictor.load(model_dir)
                            df_pred = read_csv_optional_cudf(uploaded_pred)
                            if hasattr(df_pred, "to_pandas"):
                                df_pred = df_pred.to_pandas()
                            if mp.label in df_pred.columns:
                                df_pred = df_pred.drop(columns=[mp.label])
                            preds = mp.predict(df_pred)
                            df_out = df_pred.copy(); df_out["prediction"] = preds
                    else:
                        tp = TabularPredictor.load(model_dir)
                        df_pred = read_csv_optional_cudf(uploaded_pred)
                        if hasattr(df_pred, "to_pandas"):
                            df_pred = df_pred.to_pandas()
                        if tp.label in df_pred.columns:
                            df_pred = df_pred.drop(columns=[tp.label])
                        preds = tp.predict(df_pred)
                        df_out = df_pred.copy(); df_out["prediction"] = preds
                    st.dataframe(df_out.head(100))
                    csvb = df_out.to_csv(index=False).encode("utf-8")
                    st.download_button("Download predictions.csv", csvb, file_name="predictions.csv", mime="text/csv")
                    st.session_state["last_batch_predictions"] = df_out
                except Exception as e:
                    st.error(f"Batch prediction failed: {e}")
        elif pred_mode == "Single record":
            single_text = st.text_area("Single record JSON (leave empty to auto-fill from processed_data)", height=140)
            if st.button("Predict single"):
                try:
                    if single_text.strip():
                        row = json.loads(single_text)
                        row_df = pd.DataFrame([row])
                    else:
                        if "processed_data" in st.session_state:
                            template = st.session_state["processed_data"]
                            row = {}
                            for c in template.columns:
                                if pd.api.types.is_numeric_dtype(template[c]):
                                    row[c] = float(template[c].mean())
                                else:
                                    row[c] = template[c].mode().iloc[0] if not template[c].mode().mode().empty else ""
                            row_df = pd.DataFrame([row])
                        else:
                            st.error("No processed_data to auto-fill from. Provide JSON instead.")
                            row_df = None
                    if row_df is not None:
                        if ptype == "multimodal" and _HAS_MULTIMODAL:
                            mp = MultiModalPredictor.load(model_dir)
                            preds = mp.predict(row_df)
                        else:
                            tp = TabularPredictor.load(model_dir)
                            preds = tp.predict(row_df)
                        st.success(f"Prediction: {preds.tolist()}")
                except Exception as e:
                    st.error(f"Single prediction failed: {e}")
        else:
            st.write("Stream demo — sequentially show predictions")
            if st.button("Start stream demo"):
                try:
                    predictor = TabularPredictor.load(model_dir) if ptype == "tabular" else MultiModalPredictor.load(model_dir)
                except Exception as e:
                    st.error(f"Failed to load predictor: {e}")
                    predictor = None
                if predictor is not None:
                    df_stream = st.session_state.get("processed_data", st.session_state.get("data")).sample(frac=1.0).reset_index(drop=True).head(50)
                    ph = st.empty()
                    for i in range(len(df_stream)):
                        row = df_stream.iloc[[i]]
                        try:
                            X = row.drop(columns=[predictor.label]) if predictor.label in row.columns else row
                            p = predictor.predict(X)
                            ph.write({"index": i, "prediction": p.tolist()})
                        except Exception as e:
                            ph.write({"index": i, "error": str(e)})
                        time.sleep(0.3)
                    st.success("Stream demo finished.")

# ---------- Step 8: Export & Lightweight API server ----------
if page == "8. Export":
    st.header("Step 8 — Export, Download & Lightweight API Server")
    if "predictor_path" not in st.session_state:
        st.info("Train a model first (Step 4).")
    else:
        model_dir = st.session_state["predictor_path"]
        st.write(f"Model dir: {model_dir}")
        if st.button("Create ZIP of model artifacts"):
            try:
                mem = io.BytesIO()
                with zipfile.ZipFile(mem, mode="w", compression=zipfile.ZIP_DEFLATED) as zf:
                    for root, _, files in os.walk(model_dir):
                        for f in files:
                            full = os.path.join(root, f)
                            arc = os.path.relpath(full, model_dir)
                            zf.write(full, arc)
                mem.seek(0)
                st.download_button("Download model.zip", mem, file_name="autogluon_model.zip", mime="application/zip")
            except Exception as e:
                st.error(f"Failed to create zip: {e}")
        if "last_batch_predictions" in st.session_state:
            dfp = st.session_state["last_batch_predictions"]
            st.download_button("Download last predictions", dfp.to_csv(index=False).encode("utf-8"), file_name="last_predictions.csv", mime="text/csv")

        st.markdown("### Lightweight local API server")
        api_port = st.number_input("API port", min_value=8000, max_value=9999, value=8000, step=1)
        start = st.button("Start API server")
        stop = st.button("Stop API server")
        # manage subprocess record
        if "api_proc" not in st.session_state:
            st.session_state["api_proc"] = None
        def write_server_script(path, model_path, predictor_type):
            code = f"""import uvicorn, json, os, io, sys
from fastapi import FastAPI, UploadFile, File
import pandas as pd
app = FastAPI()
MODEL_PATH = r'{model_path}'
PRED_TYPE = '{predictor_type}'
from autogluon.tabular import TabularPredictor
try:
    if PRED_TYPE=='multimodal':
        from autogluon.multimodal import MultiModalPredictor as MMP
        PRED = MMP.load(MODEL_PATH)
    else:
        PRED = TabularPredictor.load(MODEL_PATH)
except Exception as e:
    PRED = None
@app.post('/predict')
async def predict(file: UploadFile = File(...)):
    contents = await file.read()
    df = pd.read_csv(io.BytesIO(contents))
    if PRED is None:
        return {'error':'model not loaded'}
    X = df.drop(columns=[PRED.label]) if PRED.label in df.columns else df
    preds = PRED.predict(X)
    return {'predictions': preds.tolist()}
if __name__=='__main__':
    uvicorn.run(app, host='0.0.0.0', port=int(sys.argv[1]) if len(sys.argv)>1 else 8000)
"""
            with open(path, 'w', encoding='utf-8') as f:
                f.write(code)
        script_path = os.path.join(tempfile.gettempdir(), 'opendataai_api_server.py')
        if start and st.session_state['api_proc'] is None:
            try:
                write_server_script(script_path, model_dir, st.session_state.get('predictor_type','tabular'))
                # launch subprocess
                proc = subprocess.Popen([sys.executable, script_path, str(int(api_port))], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                st.session_state['api_proc'] = proc.pid
                st.success(f'Started API server (PID={proc.pid}) on port {api_port}')
            except Exception as e:
                st.error(f'Failed to start API server: {e}')
        if stop and st.session_state.get('api_proc') is not None:
            try:
                pid = st.session_state['api_proc']
                os.kill(pid, signal.SIGTERM)
                st.session_state['api_proc'] = None
                st.success('API server stopped (sent SIGTERM)')
            except Exception as e:
                st.error(f'Failed to stop API server: {e}')
st.markdown('---')
st.caption('End of workflow: ingestion → preprocessing → training → evaluation → ablation → prediction → export')
