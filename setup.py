"""
Setup script for OpenDataAI No-Code ML Platform.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
if requirements_path.exists():
    with open(requirements_path, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
else:
    requirements = [
        "streamlit>=1.28.0",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "scikit-learn>=1.3.0",
        "autogluon.tabular>=1.0.0",
        "autogluon.multimodal>=1.0.0",
        "plotly>=5.15.0",
        "fastapi>=0.100.0",
        "uvicorn>=0.23.0",
    ]

setup(
    name="opendataai-platform",
    version="2.0.0",
    author="OpenDataAI Team",
    author_email="<EMAIL>",
    description="No-Code AI & Data Platform built with Streamlit and AutoGluon",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/opendataai/no-code-ml-platform",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Data Scientists",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "gpu": [
            "cudf>=23.08.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "opendataai=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": ["*.py"],
    },
    zip_safe=False,
)
