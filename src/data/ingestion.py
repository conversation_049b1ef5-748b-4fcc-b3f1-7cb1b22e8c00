"""
Data ingestion module for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import Optional, Union, Dict, Any, <PERSON>ple
import tempfile
import zipfile
import json
import logging
from pathlib import Path
from io import BytesIO, StringIO

from ..models.exceptions import DataProcessingError, FileProcessingError
from ..models.data_models import DataInfo
from .utils import DataUtils
from .validation import DataValidator

logger = logging.getLogger(__name__)


class DataIngestionManager:
    """Manager class for data ingestion operations."""
    
    def __init__(self):
        self.validator = DataValidator()
        self.supported_formats = {
            'csv': self._read_csv,
            'json': self._read_json,
            'xlsx': self._read_excel,
            'parquet': self._read_parquet,
            'zip': self._extract_zip
        }
    
    def load_data(
        self, 
        file_source: Union[str, Path, BytesIO], 
        file_format: Optional[str] = None,
        **kwargs
    ) -> Tuple[pd.<PERSON><PERSON>rame, DataInfo]:
        """
        Load data from various sources and formats.
        
        Args:
            file_source: File path, URL, or file-like object
            file_format: File format (auto-detected if None)
            **kwargs: Additional arguments for specific readers
            
        Returns:
            Tuple of (DataFrame, DataInfo)
            
        Raises:
            DataProcessingError: If data loading fails
        """
        try:
            # Determine file format
            if file_format is None:
                file_format = self._detect_format(file_source)
            
            file_format = file_format.lower()
            
            if file_format not in self.supported_formats:
                raise DataProcessingError(
                    f"Unsupported file format: {file_format}",
                    f"Supported formats: {list(self.supported_formats.keys())}"
                )
            
            # Load data using appropriate method
            logger.info(f"Loading data with format: {file_format}")
            df = self.supported_formats[file_format](file_source, **kwargs)
            
            # Validate loaded data
            DataUtils.validate_dataframe(df)
            
            # Create data info
            data_info = DataInfo.from_dataframe(df, name=str(file_source))
            
            logger.info(f"Successfully loaded data: {df.shape}")
            return df, data_info
            
        except Exception as e:
            if isinstance(e, (DataProcessingError, FileProcessingError)):
                raise
            else:
                raise DataProcessingError(f"Failed to load data from {file_source}", str(e))
    
    def load_demo_dataset(self, dataset_name: str = "california_housing") -> Tuple[pd.DataFrame, DataInfo]:
        """
        Load a demo dataset for testing.
        
        Args:
            dataset_name: Name of demo dataset
            
        Returns:
            Tuple of (DataFrame, DataInfo)
        """
        try:
            if dataset_name == "california_housing":
                from sklearn.datasets import fetch_california_housing
                data = fetch_california_housing(as_frame=True)
                df = data.frame
                
            elif dataset_name == "iris":
                from sklearn.datasets import load_iris
                data = load_iris(as_frame=True)
                df = data.frame
                df['target'] = data.target
                
            elif dataset_name == "wine":
                from sklearn.datasets import load_wine
                data = load_wine(as_frame=True)
                df = data.frame
                df['target'] = data.target
                
            elif dataset_name == "breast_cancer":
                from sklearn.datasets import load_breast_cancer
                data = load_breast_cancer(as_frame=True)
                df = data.frame
                df['target'] = data.target
                
            else:
                raise DataProcessingError(f"Unknown demo dataset: {dataset_name}")
            
            data_info = DataInfo.from_dataframe(df, name=f"demo_{dataset_name}")
            logger.info(f"Loaded demo dataset '{dataset_name}': {df.shape}")
            
            return df, data_info
            
        except Exception as e:
            raise DataProcessingError(f"Failed to load demo dataset: {dataset_name}", str(e))
    
    def _detect_format(self, file_source: Union[str, Path, BytesIO]) -> str:
        """Detect file format from file extension or content."""
        if isinstance(file_source, (str, Path)):
            path = Path(file_source)
            extension = path.suffix.lower().lstrip('.')
            
            if extension in self.supported_formats:
                return extension
            
            # Try to detect from filename
            if 'csv' in path.name.lower():
                return 'csv'
            elif 'json' in path.name.lower():
                return 'json'
        
        # Default to CSV for unknown formats
        return 'csv'
    
    def _read_csv(self, file_source: Union[str, Path, BytesIO], **kwargs) -> pd.DataFrame:
        """Read CSV file with optimizations."""
        default_kwargs = {
            'encoding': 'utf-8',
            'low_memory': False
        }
        default_kwargs.update(kwargs)
        
        try:
            if isinstance(file_source, (str, Path)):
                return DataUtils.read_csv_with_cudf(file_source, **default_kwargs)
            else:
                # For file-like objects, use pandas directly
                return pd.read_csv(file_source, **default_kwargs)
                
        except Exception as e:
            # Try with different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    default_kwargs['encoding'] = encoding
                    if isinstance(file_source, (str, Path)):
                        return pd.read_csv(file_source, **default_kwargs)
                    else:
                        file_source.seek(0)  # Reset file pointer
                        return pd.read_csv(file_source, **default_kwargs)
                except:
                    continue
            
            raise FileProcessingError(f"Failed to read CSV file", str(e))
    
    def _read_json(self, file_source: Union[str, Path, BytesIO], **kwargs) -> pd.DataFrame:
        """Read JSON file."""
        default_kwargs = {
            'orient': 'records'
        }
        default_kwargs.update(kwargs)
        
        try:
            return pd.read_json(file_source, **default_kwargs)
        except Exception as e:
            # Try different orientations
            for orient in ['index', 'values', 'split', 'table']:
                try:
                    default_kwargs['orient'] = orient
                    return pd.read_json(file_source, **default_kwargs)
                except:
                    continue
            
            raise FileProcessingError(f"Failed to read JSON file", str(e))
    
    def _read_excel(self, file_source: Union[str, Path, BytesIO], **kwargs) -> pd.DataFrame:
        """Read Excel file."""
        try:
            return pd.read_excel(file_source, **kwargs)
        except Exception as e:
            raise FileProcessingError(f"Failed to read Excel file", str(e))
    
    def _read_parquet(self, file_source: Union[str, Path, BytesIO], **kwargs) -> pd.DataFrame:
        """Read Parquet file."""
        try:
            return pd.read_parquet(file_source, **kwargs)
        except Exception as e:
            raise FileProcessingError(f"Failed to read Parquet file", str(e))
    
    def _extract_zip(self, file_source: Union[str, Path, BytesIO], **kwargs) -> pd.DataFrame:
        """
        Extract ZIP file and return information about contents.
        This is mainly for image datasets.
        """
        try:
            temp_dir = tempfile.mkdtemp(prefix="extracted_")
            
            if isinstance(file_source, (str, Path)):
                with zipfile.ZipFile(file_source, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
            else:
                with zipfile.ZipFile(file_source, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
            
            # Create a DataFrame with file information
            files_info = []
            for file_path in Path(temp_dir).rglob("*"):
                if file_path.is_file():
                    relative_path = file_path.relative_to(temp_dir)
                    files_info.append({
                        'filename': file_path.name,
                        'relative_path': str(relative_path),
                        'full_path': str(file_path),
                        'size_bytes': file_path.stat().st_size,
                        'extension': file_path.suffix.lower()
                    })
            
            if not files_info:
                raise FileProcessingError("No files found in ZIP archive")
            
            df = pd.DataFrame(files_info)
            
            # Store extraction directory for later use
            df.attrs['extraction_dir'] = temp_dir
            
            logger.info(f"Extracted {len(files_info)} files from ZIP archive to {temp_dir}")
            return df
            
        except Exception as e:
            raise FileProcessingError(f"Failed to extract ZIP file", str(e))
    
    def create_image_mapping(
        self, 
        df: pd.DataFrame, 
        filename_column: str, 
        extraction_dir: str
    ) -> pd.DataFrame:
        """
        Create image path mapping for multimodal datasets.
        
        Args:
            df: DataFrame containing filename column
            filename_column: Column containing image filenames
            extraction_dir: Directory where images were extracted
            
        Returns:
            DataFrame with added 'image' column containing full paths
        """
        try:
            def map_image_path(filename):
                if pd.isna(filename):
                    return ""
                
                # Try to find the file in extraction directory
                for file_path in Path(extraction_dir).rglob(str(filename)):
                    if file_path.is_file():
                        return str(file_path)
                
                # If not found, return empty string
                return ""
            
            df_copy = df.copy()
            df_copy['image'] = df_copy[filename_column].apply(map_image_path)
            
            # Count successful mappings
            successful_mappings = (df_copy['image'] != "").sum()
            total_rows = len(df_copy)
            
            logger.info(f"Mapped {successful_mappings}/{total_rows} image paths")
            
            if successful_mappings == 0:
                logger.warning("No image files were successfully mapped")
            
            return df_copy

        except Exception as e:
            raise DataProcessingError(f"Failed to create image mapping", str(e))

    def get_supported_formats(self) -> Dict[str, str]:
        """Get dictionary of supported file formats and descriptions."""
        return {
            'csv': 'Comma-separated values',
            'json': 'JavaScript Object Notation',
            'xlsx': 'Microsoft Excel',
            'parquet': 'Apache Parquet',
            'zip': 'ZIP archive (for images)'
        }
