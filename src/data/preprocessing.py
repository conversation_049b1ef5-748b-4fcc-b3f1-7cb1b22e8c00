"""
Data preprocessing module for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any, Union
import logging
from datetime import datetime
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, LabelEncoder
from sklearn.impute import SimpleImputer

from ..models.exceptions import DataProcessingError
from ..models.config import PreprocessingConfig
from ..models.data_models import ProcessingStep, DataInfo
from .utils import DataUtils

logger = logging.getLogger(__name__)


class DataPreprocessor:
    """Class for data preprocessing operations."""
    
    def __init__(self, config: Optional[PreprocessingConfig] = None):
        self.config = config or PreprocessingConfig()
        self.processing_steps: List[ProcessingStep] = []
        self.fitted_transformers = {}
        
    def preprocess_data(
        self, 
        df: pd.DataFrame, 
        target_column: Optional[str] = None
    ) -> Tuple[pd.<PERSON>Frame, List[ProcessingStep]]:
        """
        Apply complete preprocessing pipeline.
        
        Args:
            df: Input DataFrame
            target_column: Target column name (excluded from preprocessing)
            
        Returns:
            Tuple of (processed_df, processing_steps)
        """
        try:
            start_time = datetime.now()
            df_processed = df.copy()
            self.processing_steps = []
            
            # Column selection
            if self.config.selected_columns:
                df_processed = self._select_columns(df_processed, self.config.selected_columns)
            
            if self.config.exclude_columns:
                df_processed = self._exclude_columns(df_processed, self.config.exclude_columns)
            
            # Separate features and target
            feature_columns = [col for col in df_processed.columns if col != target_column]
            
            # Handle missing values
            df_processed = self._handle_missing_values(df_processed, feature_columns)
            
            # Feature engineering
            if self.config.extract_date_parts:
                df_processed = self._extract_date_features(df_processed, feature_columns)
            
            if self.config.create_interaction_features:
                df_processed = self._create_interaction_features(df_processed, feature_columns)
            
            # Encoding
            if self.config.enable_onehot:
                df_processed = self._apply_onehot_encoding(df_processed, feature_columns)
            
            if self.config.enable_label_encoding:
                df_processed = self._apply_label_encoding(df_processed, feature_columns)
            
            # Scaling
            if self.config.enable_scaling:
                df_processed = self._apply_scaling(df_processed, feature_columns, target_column)
            
            total_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Preprocessing completed in {total_time:.2f} seconds")
            
            return df_processed, self.processing_steps
            
        except Exception as e:
            raise DataProcessingError(f"Preprocessing failed", str(e))
    
    def _select_columns(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """Select specific columns."""
        start_time = datetime.now()
        
        missing_cols = [col for col in columns if col not in df.columns]
        if missing_cols:
            raise DataProcessingError(f"Columns not found: {missing_cols}")
        
        df_selected = df[columns].copy()
        
        step = ProcessingStep(
            step_name="column_selection",
            step_type="selection",
            parameters={"selected_columns": columns},
            applied_columns=columns,
            execution_time=(datetime.now() - start_time).total_seconds(),
            input_shape=df.shape,
            output_shape=df_selected.shape,
            columns_removed=[col for col in df.columns if col not in columns]
        )
        self.processing_steps.append(step)
        
        return df_selected
    
    def _exclude_columns(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """Exclude specific columns."""
        start_time = datetime.now()
        
        existing_cols = [col for col in columns if col in df.columns]
        df_filtered = df.drop(columns=existing_cols)
        
        step = ProcessingStep(
            step_name="column_exclusion",
            step_type="selection",
            parameters={"excluded_columns": columns},
            applied_columns=existing_cols,
            execution_time=(datetime.now() - start_time).total_seconds(),
            input_shape=df.shape,
            output_shape=df_filtered.shape,
            columns_removed=existing_cols
        )
        self.processing_steps.append(step)
        
        return df_filtered
    
    def _handle_missing_values(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """Handle missing values in the dataset."""
        start_time = datetime.now()
        df_imputed = df.copy()
        
        # Numeric imputation
        numeric_cols = [col for col in feature_columns 
                       if col in df.columns and pd.api.types.is_numeric_dtype(df[col])]
        
        if numeric_cols:
            numeric_cols_with_nulls = [col for col in numeric_cols if df[col].isnull().any()]
            
            if numeric_cols_with_nulls:
                if self.config.numeric_imputation == "constant":
                    df_imputed[numeric_cols_with_nulls] = df_imputed[numeric_cols_with_nulls].fillna(
                        self.config.numeric_fill_value
                    )
                else:
                    imputer = SimpleImputer(strategy=self.config.numeric_imputation)
                    df_imputed[numeric_cols_with_nulls] = imputer.fit_transform(
                        df_imputed[numeric_cols_with_nulls]
                    )
                    self.fitted_transformers['numeric_imputer'] = imputer
        
        # Categorical imputation
        categorical_cols = [col for col in feature_columns 
                           if col in df.columns and not pd.api.types.is_numeric_dtype(df[col])]
        
        if categorical_cols:
            categorical_cols_with_nulls = [col for col in categorical_cols if df[col].isnull().any()]
            
            if categorical_cols_with_nulls:
                if self.config.categorical_imputation == "constant":
                    df_imputed[categorical_cols_with_nulls] = df_imputed[categorical_cols_with_nulls].fillna(
                        self.config.categorical_fill_value
                    )
                else:
                    # Mode imputation
                    for col in categorical_cols_with_nulls:
                        mode_value = df_imputed[col].mode()
                        fill_value = mode_value.iloc[0] if not mode_value.empty else self.config.categorical_fill_value
                        df_imputed[col] = df_imputed[col].fillna(fill_value)
        
        step = ProcessingStep(
            step_name="missing_value_imputation",
            step_type="imputation",
            parameters={
                "numeric_strategy": self.config.numeric_imputation,
                "categorical_strategy": self.config.categorical_imputation,
                "numeric_fill_value": self.config.numeric_fill_value,
                "categorical_fill_value": self.config.categorical_fill_value
            },
            applied_columns=numeric_cols + categorical_cols,
            execution_time=(datetime.now() - start_time).total_seconds(),
            input_shape=df.shape,
            output_shape=df_imputed.shape
        )
        self.processing_steps.append(step)
        
        return df_imputed
    
    def _extract_date_features(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """Extract features from datetime columns."""
        start_time = datetime.now()
        df_with_dates = df.copy()
        new_columns = []
        
        for col in feature_columns:
            if col not in df.columns:
                continue
                
            # Try to convert to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(df[col]):
                try:
                    df_with_dates[col] = pd.to_datetime(df[col], errors='coerce')
                except:
                    continue
            
            if pd.api.types.is_datetime64_any_dtype(df_with_dates[col]):
                # Extract date parts
                df_with_dates[f"{col}__year"] = df_with_dates[col].dt.year
                df_with_dates[f"{col}__month"] = df_with_dates[col].dt.month
                df_with_dates[f"{col}__day"] = df_with_dates[col].dt.day
                df_with_dates[f"{col}__dayofweek"] = df_with_dates[col].dt.dayofweek
                df_with_dates[f"{col}__quarter"] = df_with_dates[col].dt.quarter
                
                new_columns.extend([
                    f"{col}__year", f"{col}__month", f"{col}__day",
                    f"{col}__dayofweek", f"{col}__quarter"
                ])
        
        if new_columns:
            step = ProcessingStep(
                step_name="date_feature_extraction",
                step_type="feature_engineering",
                parameters={"extracted_features": ["year", "month", "day", "dayofweek", "quarter"]},
                applied_columns=[col for col in feature_columns if pd.api.types.is_datetime64_any_dtype(df_with_dates[col])],
                execution_time=(datetime.now() - start_time).total_seconds(),
                input_shape=df.shape,
                output_shape=df_with_dates.shape,
                columns_added=new_columns
            )
            self.processing_steps.append(step)
        
        return df_with_dates

    def _create_interaction_features(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """Create interaction features between numeric columns."""
        start_time = datetime.now()
        df_with_interactions = df.copy()
        new_columns = []

        numeric_cols = [col for col in feature_columns
                       if col in df.columns and pd.api.types.is_numeric_dtype(df[col])]

        # Limit to prevent explosion of features
        max_interactions = 10
        interaction_count = 0

        for i, col1 in enumerate(numeric_cols):
            for col2 in numeric_cols[i+1:]:
                if interaction_count >= max_interactions:
                    break

                # Create multiplication interaction
                interaction_col = f"{col1}__x__{col2}"
                df_with_interactions[interaction_col] = df_with_interactions[col1] * df_with_interactions[col2]
                new_columns.append(interaction_col)
                interaction_count += 1

            if interaction_count >= max_interactions:
                break

        if new_columns:
            step = ProcessingStep(
                step_name="interaction_feature_creation",
                step_type="feature_engineering",
                parameters={"max_interactions": max_interactions, "interaction_type": "multiplication"},
                applied_columns=numeric_cols,
                execution_time=(datetime.now() - start_time).total_seconds(),
                input_shape=df.shape,
                output_shape=df_with_interactions.shape,
                columns_added=new_columns
            )
            self.processing_steps.append(step)

        return df_with_interactions

    def _apply_onehot_encoding(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """Apply one-hot encoding to categorical columns with few categories."""
        start_time = datetime.now()
        df_encoded = df.copy()
        encoded_columns = []
        new_columns = []

        categorical_cols = [col for col in feature_columns
                           if col in df.columns and not pd.api.types.is_numeric_dtype(df[col])]

        for col in categorical_cols:
            unique_count = df[col].nunique()
            if unique_count <= self.config.onehot_max_categories and unique_count > 1:
                # Apply one-hot encoding
                dummies = pd.get_dummies(df_encoded[col], prefix=col, dummy_na=False)
                df_encoded = pd.concat([df_encoded.drop(columns=[col]), dummies], axis=1)
                encoded_columns.append(col)
                new_columns.extend(dummies.columns.tolist())

        if encoded_columns:
            step = ProcessingStep(
                step_name="onehot_encoding",
                step_type="encoding",
                parameters={"max_categories": self.config.onehot_max_categories},
                applied_columns=encoded_columns,
                execution_time=(datetime.now() - start_time).total_seconds(),
                input_shape=df.shape,
                output_shape=df_encoded.shape,
                columns_added=new_columns,
                columns_removed=encoded_columns
            )
            self.processing_steps.append(step)

        return df_encoded

    def _apply_label_encoding(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """Apply label encoding to categorical columns with many categories."""
        start_time = datetime.now()
        df_encoded = df.copy()
        encoded_columns = []

        categorical_cols = [col for col in feature_columns
                           if col in df.columns and not pd.api.types.is_numeric_dtype(df[col])]

        for col in categorical_cols:
            unique_count = df[col].nunique()
            if unique_count > self.config.onehot_max_categories:
                # Apply label encoding
                le = LabelEncoder()
                df_encoded[col] = le.fit_transform(df_encoded[col].astype(str))
                self.fitted_transformers[f'label_encoder_{col}'] = le
                encoded_columns.append(col)

        if encoded_columns:
            step = ProcessingStep(
                step_name="label_encoding",
                step_type="encoding",
                parameters={"min_categories_for_label": self.config.onehot_max_categories + 1},
                applied_columns=encoded_columns,
                execution_time=(datetime.now() - start_time).total_seconds(),
                input_shape=df.shape,
                output_shape=df_encoded.shape
            )
            self.processing_steps.append(step)

        return df_encoded

    def _apply_scaling(self, df: pd.DataFrame, feature_columns: List[str], target_column: Optional[str]) -> pd.DataFrame:
        """Apply scaling to numeric columns."""
        start_time = datetime.now()
        df_scaled = df.copy()

        numeric_cols = [col for col in feature_columns
                       if col in df.columns and pd.api.types.is_numeric_dtype(df[col]) and col != target_column]

        if not numeric_cols:
            return df_scaled

        # Choose scaler
        if self.config.scaling_method == "standard":
            scaler = StandardScaler()
        elif self.config.scaling_method == "minmax":
            scaler = MinMaxScaler()
        elif self.config.scaling_method == "robust":
            scaler = RobustScaler()
        else:
            raise DataProcessingError(f"Unknown scaling method: {self.config.scaling_method}")

        # Apply scaling
        df_scaled[numeric_cols] = scaler.fit_transform(df_scaled[numeric_cols])
        self.fitted_transformers['scaler'] = scaler

        step = ProcessingStep(
            step_name="feature_scaling",
            step_type="scaling",
            parameters={"scaling_method": self.config.scaling_method},
            applied_columns=numeric_cols,
            execution_time=(datetime.now() - start_time).total_seconds(),
            input_shape=df.shape,
            output_shape=df_scaled.shape
        )
        self.processing_steps.append(step)

        return df_scaled

    def transform_new_data(self, df: pd.DataFrame, target_column: Optional[str] = None) -> pd.DataFrame:
        """
        Transform new data using fitted transformers.

        Args:
            df: New data to transform
            target_column: Target column to exclude from transformation

        Returns:
            Transformed DataFrame
        """
        try:
            df_transformed = df.copy()

            # Apply fitted transformers in order
            for step in self.processing_steps:
                if step.step_type == "imputation" and "numeric_imputer" in self.fitted_transformers:
                    numeric_cols = [col for col in step.applied_columns
                                   if col in df_transformed.columns and pd.api.types.is_numeric_dtype(df_transformed[col])]
                    if numeric_cols:
                        imputer = self.fitted_transformers["numeric_imputer"]
                        df_transformed[numeric_cols] = imputer.transform(df_transformed[numeric_cols])

                elif step.step_type == "encoding":
                    for col in step.applied_columns:
                        if col in df_transformed.columns and f'label_encoder_{col}' in self.fitted_transformers:
                            le = self.fitted_transformers[f'label_encoder_{col}']
                            # Handle unseen categories
                            df_transformed[col] = df_transformed[col].astype(str)
                            mask = df_transformed[col].isin(le.classes_)
                            df_transformed.loc[mask, col] = le.transform(df_transformed.loc[mask, col])
                            df_transformed.loc[~mask, col] = -1  # Assign -1 to unseen categories

                elif step.step_type == "scaling" and "scaler" in self.fitted_transformers:
                    numeric_cols = [col for col in step.applied_columns
                                   if col in df_transformed.columns and col != target_column]
                    if numeric_cols:
                        scaler = self.fitted_transformers["scaler"]
                        df_transformed[numeric_cols] = scaler.transform(df_transformed[numeric_cols])

            return df_transformed

        except Exception as e:
            raise DataProcessingError(f"Failed to transform new data", str(e))

    def get_preprocessing_summary(self) -> Dict[str, Any]:
        """Get summary of preprocessing steps applied."""
        return {
            'total_steps': len(self.processing_steps),
            'steps': [
                {
                    'name': step.step_name,
                    'type': step.step_type,
                    'columns_affected': len(step.applied_columns),
                    'execution_time': step.execution_time
                }
                for step in self.processing_steps
            ],
            'total_execution_time': sum(step.execution_time for step in self.processing_steps),
            'fitted_transformers': list(self.fitted_transformers.keys())
        }
