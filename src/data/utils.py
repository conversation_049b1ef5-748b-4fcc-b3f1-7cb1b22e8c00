"""
Utility functions for data processing.
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional, Union, Any
import logging
from pathlib import Path

from ..models.exceptions import DataProcessingError

logger = logging.getLogger(__name__)


class DataUtils:
    """Utility class for common data operations."""
    
    @staticmethod
    def check_cudf_availability() -> bool:
        """Check if cuDF is available for GPU acceleration."""
        try:
            import cudf
            return True
        except ImportError:
            return False
    
    @staticmethod
    def read_csv_with_cudf(file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
        """
        Read CSV file with cuDF if available, fallback to pandas.
        
        Args:
            file_path: Path to CSV file
            **kwargs: Additional arguments for read_csv
            
        Returns:
            pandas DataFrame
            
        Raises:
            DataProcessingError: If file cannot be read
        """
        try:
            if DataUtils.check_cudf_availability():
                import cudf
                logger.info("Using cuDF for CSV reading")
                gdf = cudf.read_csv(file_path, **kwargs)
                return gdf.to_pandas()
            else:
                logger.info("Using pandas for CSV reading")
                return pd.read_csv(file_path, **kwargs)
        except Exception as e:
            raise DataProcessingError(f"Failed to read CSV file: {file_path}", str(e))
    
    @staticmethod
    def safe_preview(
        df: pd.DataFrame, 
        max_rows: int = 200, 
        random_state: int = 42
    ) -> Tuple[pd.DataFrame, str]:
        """
        Create a safe preview of a DataFrame.
        
        Args:
            df: Input DataFrame
            max_rows: Maximum number of rows to show
            random_state: Random state for sampling
            
        Returns:
            Tuple of (preview_df, message)
        """
        if df is None:
            return pd.DataFrame(), "No data available"
        
        try:
            n_rows = len(df)
            if n_rows > max_rows:
                preview_df = df.sample(n=max_rows, random_state=random_state)
                message = f"Preview: sampling {max_rows} rows of {n_rows} total"
            else:
                preview_df = df.head(max_rows)
                message = f"Preview: showing {n_rows} rows"
            
            return preview_df, message
            
        except Exception as e:
            logger.warning(f"Error creating preview: {e}")
            return df.head(50), f"Preview limited due to error: {e}"
    
    @staticmethod
    def infer_problem_type(target_series: pd.Series) -> str:
        """
        Infer the problem type from target variable.
        
        Args:
            target_series: Target variable series
            
        Returns:
            Problem type: 'regression', 'binary', or 'multiclass'
        """
        if pd.api.types.is_numeric_dtype(target_series):
            unique_values = target_series.nunique()
            if unique_values > 20:
                return "regression"
            elif unique_values == 2:
                return "binary"
            else:
                return "multiclass"
        else:
            unique_values = target_series.nunique()
            if unique_values == 2:
                return "binary"
            else:
                return "multiclass"
    
    @staticmethod
    def get_column_types(df: pd.DataFrame) -> dict:
        """
        Get detailed column type information.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Dictionary with column type information
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        boolean_cols = df.select_dtypes(include=['bool']).columns.tolist()
        
        return {
            'numeric': numeric_cols,
            'categorical': categorical_cols,
            'datetime': datetime_cols,
            'boolean': boolean_cols,
            'all': df.columns.tolist()
        }
    
    @staticmethod
    def calculate_memory_usage(df: pd.DataFrame) -> dict:
        """
        Calculate detailed memory usage information.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Dictionary with memory usage information
        """
        memory_usage = df.memory_usage(deep=True)
        total_mb = memory_usage.sum() / 1024 / 1024
        
        return {
            'total_mb': total_mb,
            'per_column_mb': (memory_usage / 1024 / 1024).to_dict(),
            'shape': df.shape,
            'dtypes': df.dtypes.astype(str).to_dict()
        }
    
    @staticmethod
    def detect_outliers(
        series: pd.Series, 
        method: str = 'iqr', 
        threshold: float = 1.5
    ) -> pd.Series:
        """
        Detect outliers in a numeric series.
        
        Args:
            series: Input series
            method: Method to use ('iqr' or 'zscore')
            threshold: Threshold for outlier detection
            
        Returns:
            Boolean series indicating outliers
        """
        if not pd.api.types.is_numeric_dtype(series):
            return pd.Series([False] * len(series), index=series.index)
        
        if method == 'iqr':
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            return (series < lower_bound) | (series > upper_bound)
        
        elif method == 'zscore':
            z_scores = np.abs((series - series.mean()) / series.std())
            return z_scores > threshold
        
        else:
            raise ValueError(f"Unknown outlier detection method: {method}")
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """
        Ensure directory exists, create if it doesn't.
        
        Args:
            path: Directory path
            
        Returns:
            Path object
        """
        path_obj = Path(path)
        path_obj.mkdir(parents=True, exist_ok=True)
        return path_obj
    
    @staticmethod
    def cleanup_directory(path: Union[str, Path]) -> None:
        """
        Clean up directory contents.
        
        Args:
            path: Directory path to clean
        """
        import shutil
        path_obj = Path(path)
        if path_obj.exists():
            shutil.rmtree(path_obj)
            logger.info(f"Cleaned up directory: {path_obj}")
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, min_rows: int = 1) -> None:
        """
        Validate DataFrame basic requirements.
        
        Args:
            df: DataFrame to validate
            min_rows: Minimum number of rows required
            
        Raises:
            DataProcessingError: If validation fails
        """
        if df is None:
            raise DataProcessingError("DataFrame is None")
        
        if df.empty:
            raise DataProcessingError("DataFrame is empty")
        
        if len(df) < min_rows:
            raise DataProcessingError(f"DataFrame has {len(df)} rows, minimum {min_rows} required")
        
        if df.columns.empty:
            raise DataProcessingError("DataFrame has no columns")
