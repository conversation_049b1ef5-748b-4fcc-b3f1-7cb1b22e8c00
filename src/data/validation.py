"""
Data validation utilities for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import logging
from pathlib import Path

from ..models.exceptions import ValidationError, DataProcessingError
from ..models.data_models import DataInfo

logger = logging.getLogger(__name__)


class DataValidator:
    """Class for validating data quality and consistency."""
    
    def __init__(self):
        self.validation_results = {}
    
    def validate_dataset(
        self, 
        df: pd.DataFrame, 
        target_column: Optional[str] = None,
        min_rows: int = 10,
        max_null_percentage: float = 0.9
    ) -> Dict[str, Any]:
        """
        Comprehensive dataset validation.
        
        Args:
            df: DataFrame to validate
            target_column: Target column name if specified
            min_rows: Minimum number of rows required
            max_null_percentage: Maximum percentage of nulls allowed per column
            
        Returns:
            Dictionary with validation results
            
        Raises:
            ValidationError: If critical validation fails
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        try:
            # Basic structure validation
            self._validate_basic_structure(df, min_rows, results)
            
            # Column validation
            self._validate_columns(df, max_null_percentage, results)
            
            # Target column validation
            if target_column:
                self._validate_target_column(df, target_column, results)
            
            # Data quality checks
            self._validate_data_quality(df, results)
            
            # Memory usage check
            self._check_memory_usage(df, results)
            
            self.validation_results = results
            
            if results['errors']:
                results['is_valid'] = False
                error_msg = "; ".join(results['errors'])
                raise ValidationError(f"Dataset validation failed: {error_msg}")
            
            return results
            
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            else:
                raise ValidationError(f"Validation process failed: {str(e)}")
    
    def _validate_basic_structure(
        self, 
        df: pd.DataFrame, 
        min_rows: int, 
        results: Dict[str, Any]
    ) -> None:
        """Validate basic DataFrame structure."""
        if df is None:
            results['errors'].append("DataFrame is None")
            return
        
        if df.empty:
            results['errors'].append("DataFrame is empty")
            return
        
        if len(df) < min_rows:
            results['errors'].append(f"Insufficient rows: {len(df)} < {min_rows}")
        
        if df.columns.empty:
            results['errors'].append("DataFrame has no columns")
        
        # Check for duplicate column names
        if df.columns.duplicated().any():
            duplicates = df.columns[df.columns.duplicated()].tolist()
            results['errors'].append(f"Duplicate column names: {duplicates}")
        
        results['info']['shape'] = df.shape
        results['info']['columns'] = df.columns.tolist()
    
    def _validate_columns(
        self, 
        df: pd.DataFrame, 
        max_null_percentage: float, 
        results: Dict[str, Any]
    ) -> None:
        """Validate individual columns."""
        null_percentages = df.isnull().sum() / len(df)
        
        # Check for columns with too many nulls
        high_null_cols = null_percentages[null_percentages > max_null_percentage]
        if not high_null_cols.empty:
            results['warnings'].append(
                f"Columns with >{max_null_percentage*100}% nulls: {high_null_cols.index.tolist()}"
            )
        
        # Check for constant columns
        constant_cols = []
        for col in df.columns:
            if df[col].nunique() <= 1:
                constant_cols.append(col)
        
        if constant_cols:
            results['warnings'].append(f"Constant columns (no variance): {constant_cols}")
        
        # Check for columns with single unique value (excluding nulls)
        single_value_cols = []
        for col in df.columns:
            non_null_values = df[col].dropna()
            if len(non_null_values) > 0 and non_null_values.nunique() == 1:
                single_value_cols.append(col)
        
        if single_value_cols:
            results['warnings'].append(f"Single-value columns: {single_value_cols}")
        
        results['info']['null_percentages'] = null_percentages.to_dict()
        results['info']['unique_counts'] = df.nunique().to_dict()
    
    def _validate_target_column(
        self, 
        df: pd.DataFrame, 
        target_column: str, 
        results: Dict[str, Any]
    ) -> None:
        """Validate target column."""
        if target_column not in df.columns:
            results['errors'].append(f"Target column '{target_column}' not found")
            return
        
        target_series = df[target_column]
        
        # Check for nulls in target
        null_count = target_series.isnull().sum()
        if null_count > 0:
            null_percentage = null_count / len(target_series) * 100
            if null_percentage > 10:  # More than 10% nulls is concerning
                results['errors'].append(
                    f"Target column has {null_percentage:.1f}% null values"
                )
            else:
                results['warnings'].append(
                    f"Target column has {null_count} null values ({null_percentage:.1f}%)"
                )
        
        # Check target distribution
        unique_count = target_series.nunique()
        if unique_count == 1:
            results['errors'].append("Target column has only one unique value")
        elif unique_count == len(target_series):
            results['warnings'].append("Target column has all unique values (possible ID column)")
        
        # For classification, check class balance
        if not pd.api.types.is_numeric_dtype(target_series) or unique_count <= 20:
            value_counts = target_series.value_counts()
            min_class_size = value_counts.min()
            max_class_size = value_counts.max()
            
            if min_class_size < 5:
                results['warnings'].append(f"Some classes have very few samples (min: {min_class_size})")
            
            # Check for severe class imbalance
            imbalance_ratio = max_class_size / min_class_size if min_class_size > 0 else float('inf')
            if imbalance_ratio > 100:
                results['warnings'].append(f"Severe class imbalance detected (ratio: {imbalance_ratio:.1f})")
        
        results['info']['target_info'] = {
            'unique_count': unique_count,
            'null_count': null_count,
            'dtype': str(target_series.dtype)
        }
    
    def _validate_data_quality(self, df: pd.DataFrame, results: Dict[str, Any]) -> None:
        """Validate overall data quality."""
        # Check for completely empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            results['warnings'].append(f"Found {empty_rows} completely empty rows")
        
        # Check for duplicate rows
        duplicate_rows = df.duplicated().sum()
        if duplicate_rows > 0:
            duplicate_percentage = duplicate_rows / len(df) * 100
            if duplicate_percentage > 5:  # More than 5% duplicates
                results['warnings'].append(
                    f"High number of duplicate rows: {duplicate_rows} ({duplicate_percentage:.1f}%)"
                )
            else:
                results['info']['duplicate_rows'] = duplicate_rows
        
        # Check for mixed data types in object columns
        mixed_type_cols = []
        for col in df.select_dtypes(include=['object']).columns:
            sample_types = df[col].dropna().apply(type).unique()
            if len(sample_types) > 1:
                mixed_type_cols.append(col)
        
        if mixed_type_cols:
            results['warnings'].append(f"Columns with mixed data types: {mixed_type_cols}")
        
        results['info']['data_quality'] = {
            'empty_rows': empty_rows,
            'duplicate_rows': duplicate_rows,
            'mixed_type_columns': mixed_type_cols
        }
    
    def _check_memory_usage(self, df: pd.DataFrame, results: Dict[str, Any]) -> None:
        """Check memory usage and provide recommendations."""
        memory_mb = df.memory_usage(deep=True).sum() / 1024 / 1024
        
        results['info']['memory_usage_mb'] = memory_mb
        
        if memory_mb > 1000:  # More than 1GB
            results['warnings'].append(f"Large dataset: {memory_mb:.1f} MB memory usage")
        
        # Check for potential memory optimizations
        optimization_suggestions = []
        
        # Check for object columns that could be categorical
        for col in df.select_dtypes(include=['object']).columns:
            unique_ratio = df[col].nunique() / len(df)
            if unique_ratio < 0.5:  # Less than 50% unique values
                optimization_suggestions.append(f"Convert '{col}' to categorical")
        
        # Check for int64 columns that could be smaller
        for col in df.select_dtypes(include=['int64']).columns:
            col_min, col_max = df[col].min(), df[col].max()
            if col_min >= 0 and col_max <= 255:
                optimization_suggestions.append(f"Convert '{col}' to uint8")
            elif col_min >= -128 and col_max <= 127:
                optimization_suggestions.append(f"Convert '{col}' to int8")
        
        if optimization_suggestions:
            results['info']['memory_optimizations'] = optimization_suggestions
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get comprehensive data summary."""
        return {
            'basic_info': {
                'shape': df.shape,
                'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
                'dtypes': df.dtypes.value_counts().to_dict()
            },
            'missing_data': {
                'total_nulls': df.isnull().sum().sum(),
                'null_percentages': (df.isnull().sum() / len(df) * 100).to_dict()
            },
            'column_stats': {
                'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
                'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist(),
                'datetime_columns': df.select_dtypes(include=['datetime64']).columns.tolist()
            },
            'data_quality': {
                'duplicate_rows': df.duplicated().sum(),
                'empty_rows': df.isnull().all(axis=1).sum(),
                'unique_counts': df.nunique().to_dict()
            }
        }
