"""
Ablation analysis module for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime
import tempfile
import shutil

from ..models.exceptions import ModelTrainingError
from ..models.config import AblationConfig
from ..models.data_models import AblationResult
from ..data.utils import DataUtils
from .training import ModelTrainer

logger = logging.getLogger(__name__)


class AblationAnalyzer:
    """Class for performing ablation studies on ML models."""
    
    def __init__(self, config: Optional[AblationConfig] = None):
        self.config = config or AblationConfig()
        self.config.validate()
        self.trainer = ModelTrainer()
    
    def run_ablation_study(
        self,
        df: pd.DataFrame,
        target_column: str,
        components_to_test: Optional[List[str]] = None
    ) -> List[AblationResult]:
        """
        Run comprehensive ablation study.
        
        Args:
            df: Training DataFrame
            target_column: Target column name
            components_to_test: List of components to test (default: ['numerical', 'categorical'])
            
        Returns:
            List of AblationResult objects sorted by importance
            
        Raises:
            ModelTrainingError: If ablation study fails
        """
        try:
            start_time = datetime.now()
            
            # Validate inputs
            self._validate_ablation_inputs(df, target_column)
            
            # Sample data if needed
            df_sample = self._sample_data(df)
            
            # Get baseline performance
            baseline_score = self._get_baseline_performance(df_sample, target_column)
            
            # Determine components to test
            if components_to_test is None:
                components_to_test = self.config.components_to_test
            
            # Run ablation tests
            results = []
            for component in components_to_test:
                try:
                    ablated_score = self._test_component_ablation(
                        df_sample, target_column, component
                    )
                    
                    result = AblationResult.from_scores(
                        component=component,
                        base_score=baseline_score,
                        ablated_score=ablated_score
                    )
                    results.append(result)
                    
                    logger.info(f"Ablation test '{component}': delta = {result.performance_delta:.4f}")
                    
                except Exception as e:
                    logger.warning(f"Failed ablation test for component '{component}': {e}")
                    continue
            
            # Sort by absolute importance
            results.sort(key=lambda x: abs(x.performance_delta), reverse=True)
            
            total_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Ablation study completed in {total_time:.2f} seconds")
            
            return results
            
        except Exception as e:
            raise ModelTrainingError(f"Ablation study failed", str(e))
    
    def run_feature_ablation(
        self,
        df: pd.DataFrame,
        target_column: str,
        features_to_test: Optional[List[str]] = None,
        max_features: int = 10
    ) -> List[AblationResult]:
        """
        Run ablation study on individual features.
        
        Args:
            df: Training DataFrame
            target_column: Target column name
            features_to_test: Specific features to test (default: all features)
            max_features: Maximum number of features to test
            
        Returns:
            List of AblationResult objects for individual features
        """
        try:
            # Sample data
            df_sample = self._sample_data(df)
            
            # Get baseline performance
            baseline_score = self._get_baseline_performance(df_sample, target_column)
            
            # Determine features to test
            if features_to_test is None:
                feature_columns = [col for col in df_sample.columns if col != target_column]
                # Limit number of features to test
                features_to_test = feature_columns[:max_features]
            
            results = []
            for feature in features_to_test:
                try:
                    # Create dataset without this feature
                    df_ablated = df_sample.drop(columns=[feature])
                    
                    # Train model without this feature
                    ablated_score = self._quick_train_and_score(df_ablated, target_column)
                    
                    result = AblationResult.from_scores(
                        component=f"feature_{feature}",
                        base_score=baseline_score,
                        ablated_score=ablated_score
                    )
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"Failed feature ablation for '{feature}': {e}")
                    continue
            
            # Sort by importance
            results.sort(key=lambda x: abs(x.performance_delta), reverse=True)
            
            return results
            
        except Exception as e:
            raise ModelTrainingError(f"Feature ablation failed", str(e))
    
    def run_data_size_ablation(
        self,
        df: pd.DataFrame,
        target_column: str,
        size_fractions: Optional[List[float]] = None
    ) -> List[Dict[str, Any]]:
        """
        Run ablation study on training data size.
        
        Args:
            df: Training DataFrame
            target_column: Target column name
            size_fractions: List of data size fractions to test
            
        Returns:
            List of dictionaries with size and performance information
        """
        try:
            if size_fractions is None:
                size_fractions = [0.1, 0.25, 0.5, 0.75, 1.0]
            
            results = []
            for fraction in size_fractions:
                try:
                    # Sample data
                    sample_size = int(len(df) * fraction)
                    df_sample = df.sample(n=sample_size, random_state=42)
                    
                    # Train and score
                    score = self._quick_train_and_score(df_sample, target_column)
                    
                    results.append({
                        'data_fraction': fraction,
                        'sample_size': sample_size,
                        'score': score,
                        'score_per_sample': score / sample_size if sample_size > 0 else 0
                    })
                    
                    logger.info(f"Data size ablation {fraction*100:.0f}%: score = {score:.4f}")
                    
                except Exception as e:
                    logger.warning(f"Failed data size ablation for fraction {fraction}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            raise ModelTrainingError(f"Data size ablation failed", str(e))
    
    def _validate_ablation_inputs(self, df: pd.DataFrame, target_column: str) -> None:
        """Validate inputs for ablation study."""
        if df is None or df.empty:
            raise ModelTrainingError("DataFrame is empty")
        
        if target_column not in df.columns:
            raise ModelTrainingError(f"Target column '{target_column}' not found")
        
        if len(df) < 20:
            raise ModelTrainingError("Insufficient data for ablation study (minimum 20 rows)")
    
    def _sample_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Sample data for ablation study."""
        sample_size = int(len(df) * self.config.sample_fraction)
        sample_size = max(sample_size, 20)  # Minimum sample size
        sample_size = min(sample_size, len(df))  # Don't exceed original size
        
        return df.sample(n=sample_size, random_state=42)
    
    def _get_baseline_performance(self, df: pd.DataFrame, target_column: str) -> float:
        """Get baseline model performance."""
        return self._quick_train_and_score(df, target_column)
    
    def _test_component_ablation(
        self,
        df: pd.DataFrame,
        target_column: str,
        component: str
    ) -> float:
        """Test ablation of a specific component."""
        if component == "numerical":
            return self._test_numerical_ablation(df, target_column)
        elif component == "categorical":
            return self._test_categorical_ablation(df, target_column)
        else:
            raise ModelTrainingError(f"Unknown component for ablation: {component}")
    
    def _test_numerical_ablation(self, df: pd.DataFrame, target_column: str) -> float:
        """Test ablation by removing numerical features."""
        feature_columns = [col for col in df.columns if col != target_column]
        numerical_columns = [col for col in feature_columns 
                           if pd.api.types.is_numeric_dtype(df[col])]
        
        if not numerical_columns:
            # No numerical columns to remove
            return self._quick_train_and_score(df, target_column)
        
        # Create dataset without numerical columns
        df_ablated = df.drop(columns=numerical_columns)
        
        # Check if we still have features
        remaining_features = [col for col in df_ablated.columns if col != target_column]
        if not remaining_features:
            logger.warning("No features remaining after numerical ablation")
            return 0.0
        
        return self._quick_train_and_score(df_ablated, target_column)
    
    def _test_categorical_ablation(self, df: pd.DataFrame, target_column: str) -> float:
        """Test ablation by removing categorical features."""
        feature_columns = [col for col in df.columns if col != target_column]
        categorical_columns = [col for col in feature_columns 
                             if not pd.api.types.is_numeric_dtype(df[col])]
        
        if not categorical_columns:
            # No categorical columns to remove
            return self._quick_train_and_score(df, target_column)
        
        # Create dataset without categorical columns
        df_ablated = df.drop(columns=categorical_columns)
        
        # Check if we still have features
        remaining_features = [col for col in df_ablated.columns if col != target_column]
        if not remaining_features:
            logger.warning("No features remaining after categorical ablation")
            return 0.0
        
        return self._quick_train_and_score(df_ablated, target_column)
    
    def _quick_train_and_score(self, df: pd.DataFrame, target_column: str) -> float:
        """Quickly train a model and return the score."""
        try:
            performance_dict = self.trainer.quick_train(
                df=df,
                target_column=target_column,
                timeout=self.config.quick_train_timeout,
                preset="medium_quality"
            )
            
            # Extract score from performance dictionary
            if not performance_dict:
                return 0.0
            
            # Try to find a numeric score
            for key, value in performance_dict.items():
                if isinstance(value, (int, float)) and not np.isnan(value):
                    return float(value)
            
            return 0.0
            
        except Exception as e:
            logger.warning(f"Quick training failed: {e}")
            return 0.0
    
    def create_ablation_report(self, results: List[AblationResult]) -> Dict[str, Any]:
        """
        Create a comprehensive ablation report.
        
        Args:
            results: List of ablation results
            
        Returns:
            Dictionary with report information
        """
        if not results:
            return {"error": "No ablation results available"}
        
        # Sort by absolute importance
        sorted_results = sorted(results, key=lambda x: abs(x.performance_delta), reverse=True)
        
        report = {
            "summary": {
                "total_components_tested": len(results),
                "most_important_component": sorted_results[0].component if sorted_results else None,
                "largest_performance_drop": max(r.performance_delta for r in results) if results else 0,
                "average_importance": np.mean([abs(r.performance_delta) for r in results]) if results else 0
            },
            "detailed_results": [
                {
                    "component": r.component,
                    "performance_delta": r.performance_delta,
                    "relative_importance": r.relative_importance,
                    "base_score": r.base_score,
                    "ablated_score": r.ablated_score
                }
                for r in sorted_results
            ],
            "recommendations": self._generate_recommendations(sorted_results)
        }
        
        return report
    
    def _generate_recommendations(self, results: List[AblationResult]) -> List[str]:
        """Generate recommendations based on ablation results."""
        recommendations = []
        
        if not results:
            return ["No ablation results available for recommendations"]
        
        # Find most important component
        most_important = results[0]
        if abs(most_important.performance_delta) > 0.1:
            recommendations.append(
                f"Component '{most_important.component}' is critical for model performance "
                f"(removing it causes {abs(most_important.performance_delta):.3f} performance drop)"
            )
        
        # Find least important components
        least_important = [r for r in results if abs(r.performance_delta) < 0.01]
        if least_important:
            components = [r.component for r in least_important]
            recommendations.append(
                f"Components {components} have minimal impact and could potentially be removed "
                f"to simplify the model"
            )
        
        # Check for negative deltas (components that hurt performance)
        harmful_components = [r for r in results if r.performance_delta < -0.05]
        if harmful_components:
            components = [r.component for r in harmful_components]
            recommendations.append(
                f"Components {components} may be hurting model performance "
                f"and should be investigated"
            )
        
        return recommendations
