"""
Model evaluation module for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, Tuple, List, Union
import logging
from datetime import datetime
from pathlib import Path

from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    mean_squared_error, mean_absolute_error, r2_score,
    classification_report, confusion_matrix
)

from autogluon.tabular import TabularPredictor

# Optional multimodal import
try:
    from autogluon.multimodal import MultiModalPredictor
    HAS_MULTIMODAL = True
except ImportError:
    MultiModalPredictor = None
    HAS_MULTIMODAL = False

from ..models.exceptions import ModelTrainingError, PredictionError
from ..models.data_models import ModelInfo

logger = logging.getLogger(__name__)


class ModelEvaluator:
    """Class for evaluating trained models."""
    
    def __init__(self):
        self.evaluation_cache = {}
    
    def evaluate_model(
        self,
        model_info: ModelInfo,
        test_data: pd.DataFrame,
        detailed: bool = True
    ) -> Dict[str, Any]:
        """
        Comprehensive model evaluation.
        
        Args:
            model_info: Information about the trained model
            test_data: Test dataset for evaluation
            detailed: Whether to include detailed metrics
            
        Returns:
            Dictionary with evaluation results
            
        Raises:
            PredictionError: If evaluation fails
        """
        try:
            start_time = datetime.now()
            
            # Load model
            predictor = self._load_predictor(model_info)
            
            # Prepare test data
            X_test, y_test = self._prepare_test_data(test_data, model_info.target_column)
            
            # Make predictions
            predictions = predictor.predict(X_test)
            
            # Get probabilities if available
            probabilities = None
            try:
                if hasattr(predictor, 'predict_proba'):
                    probabilities = predictor.predict_proba(X_test)
            except:
                pass
            
            # Calculate metrics based on problem type
            if model_info.problem_type in ['binary', 'multiclass']:
                metrics = self._calculate_classification_metrics(
                    y_test, predictions, probabilities, detailed
                )
            else:  # regression
                metrics = self._calculate_regression_metrics(
                    y_test, predictions, detailed
                )
            
            # Add general information
            evaluation_time = (datetime.now() - start_time).total_seconds()
            
            results = {
                'model_id': model_info.model_id,
                'problem_type': model_info.problem_type,
                'test_samples': len(y_test),
                'evaluation_time': evaluation_time,
                'metrics': metrics,
                'predictions_sample': predictions[:10].tolist() if len(predictions) > 0 else [],
                'actual_sample': y_test[:10].tolist() if len(y_test) > 0 else []
            }
            
            if probabilities is not None:
                results['probabilities_sample'] = probabilities[:10].tolist() if len(probabilities) > 0 else []
            
            logger.info(f"Model evaluation completed in {evaluation_time:.2f} seconds")
            
            return results
            
        except Exception as e:
            raise PredictionError(f"Model evaluation failed", str(e))
    
    def get_feature_importance(
        self,
        model_info: ModelInfo,
        data: Optional[pd.DataFrame] = None
    ) -> Optional[pd.DataFrame]:
        """
        Get feature importance from the model.
        
        Args:
            model_info: Information about the trained model
            data: Optional data for computing importance
            
        Returns:
            DataFrame with feature importance or None if not available
        """
        try:
            predictor = self._load_predictor(model_info)
            
            if hasattr(predictor, 'feature_importance'):
                importance = predictor.feature_importance(data=data)
                
                if isinstance(importance, pd.DataFrame):
                    # Ensure consistent format
                    if 'importance' in importance.columns:
                        return importance.sort_values('importance', ascending=False)
                    else:
                        # Try to find the importance column
                        numeric_cols = importance.select_dtypes(include=[np.number]).columns
                        if len(numeric_cols) > 0:
                            importance_col = numeric_cols[0]
                            importance = importance.rename(columns={importance_col: 'importance'})
                            return importance.sort_values('importance', ascending=False)
                
                return importance
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not get feature importance: {e}")
            return None
    
    def get_leaderboard(self, model_info: ModelInfo) -> Optional[pd.DataFrame]:
        """
        Get model leaderboard.
        
        Args:
            model_info: Information about the trained model
            
        Returns:
            DataFrame with leaderboard or None if not available
        """
        try:
            predictor = self._load_predictor(model_info)
            
            if hasattr(predictor, 'leaderboard'):
                return predictor.leaderboard(silent=True)
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not get leaderboard: {e}")
            return None
    
    def holdout_evaluation(
        self,
        model_info: ModelInfo,
        data: pd.DataFrame,
        test_size: float = 0.2,
        random_state: int = 42
    ) -> Dict[str, Any]:
        """
        Perform holdout evaluation on a dataset.
        
        Args:
            model_info: Information about the trained model
            data: Full dataset
            test_size: Fraction of data to use for testing
            random_state: Random state for reproducibility
            
        Returns:
            Dictionary with holdout evaluation results
        """
        try:
            # Split data
            test_data = data.sample(frac=test_size, random_state=random_state)
            
            # Evaluate
            return self.evaluate_model(model_info, test_data, detailed=True)
            
        except Exception as e:
            raise PredictionError(f"Holdout evaluation failed", str(e))
    
    def _load_predictor(self, model_info: ModelInfo):
        """Load predictor from model info."""
        if not Path(model_info.model_path).exists():
            raise PredictionError(f"Model path does not exist: {model_info.model_path}")
        
        try:
            if model_info.model_type == "multimodal" and HAS_MULTIMODAL:
                return MultiModalPredictor.load(model_info.model_path)
            else:
                return TabularPredictor.load(model_info.model_path)
        except Exception as e:
            raise PredictionError(f"Failed to load model from {model_info.model_path}", str(e))
    
    def _prepare_test_data(
        self,
        test_data: pd.DataFrame,
        target_column: str
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare test data for evaluation."""
        if target_column not in test_data.columns:
            raise PredictionError(f"Target column '{target_column}' not found in test data")
        
        X_test = test_data.drop(columns=[target_column])
        y_test = test_data[target_column]
        
        return X_test, y_test
    
    def _calculate_classification_metrics(
        self,
        y_true: pd.Series,
        y_pred: pd.Series,
        y_proba: Optional[np.ndarray],
        detailed: bool
    ) -> Dict[str, Any]:
        """Calculate classification metrics."""
        metrics = {}
        
        # Basic metrics
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        
        # Handle binary vs multiclass
        if len(np.unique(y_true)) == 2:
            # Binary classification
            metrics['precision'] = precision_score(y_true, y_pred, average='binary')
            metrics['recall'] = recall_score(y_true, y_pred, average='binary')
            metrics['f1'] = f1_score(y_true, y_pred, average='binary')
        else:
            # Multiclass classification
            metrics['precision_macro'] = precision_score(y_true, y_pred, average='macro')
            metrics['recall_macro'] = recall_score(y_true, y_pred, average='macro')
            metrics['f1_macro'] = f1_score(y_true, y_pred, average='macro')
            
            metrics['precision_weighted'] = precision_score(y_true, y_pred, average='weighted')
            metrics['recall_weighted'] = recall_score(y_true, y_pred, average='weighted')
            metrics['f1_weighted'] = f1_score(y_true, y_pred, average='weighted')
        
        if detailed:
            # Confusion matrix
            cm = confusion_matrix(y_true, y_pred)
            metrics['confusion_matrix'] = cm.tolist()
            
            # Classification report
            try:
                report = classification_report(y_true, y_pred, output_dict=True)
                metrics['classification_report'] = report
            except:
                pass
            
            # Class distribution
            metrics['class_distribution'] = y_true.value_counts().to_dict()
            metrics['prediction_distribution'] = pd.Series(y_pred).value_counts().to_dict()
        
        return metrics
    
    def _calculate_regression_metrics(
        self,
        y_true: pd.Series,
        y_pred: pd.Series,
        detailed: bool
    ) -> Dict[str, Any]:
        """Calculate regression metrics."""
        metrics = {}
        
        # Basic metrics
        metrics['mse'] = mean_squared_error(y_true, y_pred)
        metrics['rmse'] = np.sqrt(metrics['mse'])
        metrics['mae'] = mean_absolute_error(y_true, y_pred)
        metrics['r2'] = r2_score(y_true, y_pred)
        
        # Additional metrics
        metrics['mean_absolute_percentage_error'] = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        
        if detailed:
            # Residuals analysis
            residuals = y_true - y_pred
            metrics['residuals_mean'] = float(residuals.mean())
            metrics['residuals_std'] = float(residuals.std())
            metrics['residuals_min'] = float(residuals.min())
            metrics['residuals_max'] = float(residuals.max())
            
            # Target statistics
            metrics['target_mean'] = float(y_true.mean())
            metrics['target_std'] = float(y_true.std())
            metrics['target_min'] = float(y_true.min())
            metrics['target_max'] = float(y_true.max())
            
            # Prediction statistics
            metrics['prediction_mean'] = float(y_pred.mean())
            metrics['prediction_std'] = float(y_pred.std())
            metrics['prediction_min'] = float(y_pred.min())
            metrics['prediction_max'] = float(y_pred.max())
        
        return metrics
    
    def compare_models(
        self,
        model_infos: List[ModelInfo],
        test_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Compare multiple models on the same test data.
        
        Args:
            model_infos: List of model information objects
            test_data: Test dataset
            
        Returns:
            DataFrame with comparison results
        """
        comparison_results = []
        
        for model_info in model_infos:
            try:
                results = self.evaluate_model(model_info, test_data, detailed=False)
                
                row = {
                    'model_id': model_info.model_id,
                    'model_type': model_info.model_type,
                    'problem_type': model_info.problem_type,
                    'training_time': model_info.training_time,
                    'evaluation_time': results['evaluation_time']
                }
                
                # Add metrics
                row.update(results['metrics'])
                
                comparison_results.append(row)
                
            except Exception as e:
                logger.warning(f"Failed to evaluate model {model_info.model_id}: {e}")
                continue
        
        if comparison_results:
            return pd.DataFrame(comparison_results)
        else:
            return pd.DataFrame()
