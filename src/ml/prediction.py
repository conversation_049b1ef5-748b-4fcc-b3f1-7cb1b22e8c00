"""
Model prediction module for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List, Union
import logging
import json
from datetime import datetime
from pathlib import Path

from autogluon.tabular import TabularPredictor

# Optional multimodal import
try:
    from autogluon.multimodal import MultiModalPredictor
    HAS_MULTIMODAL = True
except ImportError:
    MultiModalPredictor = None
    HAS_MULTIMODAL = False

from ..models.exceptions import PredictionError
from ..models.data_models import ModelInfo, PredictionResult
from ..data.utils import DataUtils

logger = logging.getLogger(__name__)


class ModelPredictor:
    """Class for making predictions with trained models."""
    
    def __init__(self):
        self.loaded_models = {}  # Cache for loaded models
    
    def predict_batch(
        self,
        model_info: ModelInfo,
        data: pd.DataFrame,
        include_probabilities: bool = False
    ) -> PredictionResult:
        """
        Make batch predictions on a dataset.
        
        Args:
            model_info: Information about the trained model
            data: Input data for prediction
            include_probabilities: Whether to include prediction probabilities
            
        Returns:
            PredictionResult object
            
        Raises:
            PredictionError: If prediction fails
        """
        try:
            start_time = datetime.now()
            
            # Load model
            predictor = self._get_predictor(model_info)
            
            # Prepare data
            prediction_data = self._prepare_prediction_data(data, model_info)
            
            # Make predictions
            predictions = predictor.predict(prediction_data)
            
            # Get probabilities if requested and available
            probabilities = None
            if include_probabilities:
                probabilities = self._get_probabilities(predictor, prediction_data)
            
            # Calculate prediction time
            prediction_time = (datetime.now() - start_time).total_seconds()
            
            # Create result
            result = PredictionResult.from_batch_predictions(
                predictions=predictions,
                model_id=model_info.model_id,
                prediction_time=prediction_time,
                probabilities=probabilities
            )
            
            logger.info(f"Batch prediction completed: {len(predictions)} predictions in {prediction_time:.2f}s")
            
            return result
            
        except Exception as e:
            raise PredictionError(f"Batch prediction failed", str(e))
    
    def predict_single(
        self,
        model_info: ModelInfo,
        record: Union[Dict[str, Any], pd.Series, pd.DataFrame],
        include_probabilities: bool = False
    ) -> PredictionResult:
        """
        Make a single prediction.
        
        Args:
            model_info: Information about the trained model
            record: Single record for prediction (dict, Series, or single-row DataFrame)
            include_probabilities: Whether to include prediction probabilities
            
        Returns:
            PredictionResult object
            
        Raises:
            PredictionError: If prediction fails
        """
        try:
            start_time = datetime.now()
            
            # Convert record to DataFrame
            if isinstance(record, dict):
                data = pd.DataFrame([record])
            elif isinstance(record, pd.Series):
                data = record.to_frame().T
            elif isinstance(record, pd.DataFrame):
                if len(record) != 1:
                    raise PredictionError("DataFrame must contain exactly one row for single prediction")
                data = record.copy()
            else:
                raise PredictionError(f"Unsupported record type: {type(record)}")
            
            # Load model
            predictor = self._get_predictor(model_info)
            
            # Prepare data
            prediction_data = self._prepare_prediction_data(data, model_info)
            
            # Make prediction
            predictions = predictor.predict(prediction_data)
            prediction = predictions.iloc[0] if hasattr(predictions, 'iloc') else predictions[0]
            
            # Get probabilities if requested and available
            probabilities = None
            if include_probabilities:
                proba_result = self._get_probabilities(predictor, prediction_data)
                if proba_result is not None:
                    probabilities = proba_result.iloc[0].tolist() if hasattr(proba_result, 'iloc') else proba_result[0]
            
            # Calculate prediction time
            prediction_time = (datetime.now() - start_time).total_seconds()
            
            # Create result
            result = PredictionResult.from_single_prediction(
                prediction=prediction,
                model_id=model_info.model_id,
                prediction_time=prediction_time,
                probabilities=probabilities
            )
            
            logger.info(f"Single prediction completed in {prediction_time:.4f}s: {prediction}")
            
            return result
            
        except Exception as e:
            raise PredictionError(f"Single prediction failed", str(e))
    
    def predict_from_json(
        self,
        model_info: ModelInfo,
        json_data: str,
        include_probabilities: bool = False
    ) -> PredictionResult:
        """
        Make prediction from JSON string.
        
        Args:
            model_info: Information about the trained model
            json_data: JSON string containing the record
            include_probabilities: Whether to include prediction probabilities
            
        Returns:
            PredictionResult object
            
        Raises:
            PredictionError: If prediction fails
        """
        try:
            # Parse JSON
            record = json.loads(json_data)
            
            # Make prediction
            return self.predict_single(model_info, record, include_probabilities)
            
        except json.JSONDecodeError as e:
            raise PredictionError(f"Invalid JSON data", str(e))
        except Exception as e:
            raise PredictionError(f"JSON prediction failed", str(e))
    
    def create_prediction_template(
        self,
        model_info: ModelInfo,
        sample_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """
        Create a template for making predictions.
        
        Args:
            model_info: Information about the trained model
            sample_data: Optional sample data to use for template values
            
        Returns:
            Dictionary template for predictions
        """
        try:
            template = {}
            
            for column in model_info.feature_columns:
                if sample_data is not None and column in sample_data.columns:
                    # Use sample data to infer types and example values
                    col_data = sample_data[column]
                    
                    if pd.api.types.is_numeric_dtype(col_data):
                        template[column] = float(col_data.mean()) if not col_data.empty else 0.0
                    else:
                        mode_value = col_data.mode()
                        template[column] = mode_value.iloc[0] if not mode_value.empty else ""
                else:
                    # Default values
                    template[column] = 0.0  # Will need to be adjusted based on actual data types
            
            return template
            
        except Exception as e:
            logger.warning(f"Could not create prediction template: {e}")
            return {col: None for col in model_info.feature_columns}
    
    def _get_predictor(self, model_info: ModelInfo):
        """Get predictor, using cache if available."""
        model_id = model_info.model_id
        
        # Check cache
        if model_id in self.loaded_models:
            return self.loaded_models[model_id]
        
        # Load model
        if not Path(model_info.model_path).exists():
            raise PredictionError(f"Model path does not exist: {model_info.model_path}")
        
        try:
            if model_info.model_type == "multimodal" and HAS_MULTIMODAL:
                predictor = MultiModalPredictor.load(model_info.model_path)
            else:
                predictor = TabularPredictor.load(model_info.model_path)
            
            # Cache the loaded model
            self.loaded_models[model_id] = predictor
            
            return predictor
            
        except Exception as e:
            raise PredictionError(f"Failed to load model from {model_info.model_path}", str(e))
    
    def _prepare_prediction_data(self, data: pd.DataFrame, model_info: ModelInfo) -> pd.DataFrame:
        """Prepare data for prediction."""
        # Remove target column if present
        if model_info.target_column in data.columns:
            data = data.drop(columns=[model_info.target_column])
        
        # Ensure all required feature columns are present
        missing_features = [col for col in model_info.feature_columns if col not in data.columns]
        if missing_features:
            logger.warning(f"Missing feature columns: {missing_features}")
            # Add missing columns with default values
            for col in missing_features:
                data[col] = 0  # Default value - should be improved based on column type
        
        # Select only the required features in the correct order
        try:
            prediction_data = data[model_info.feature_columns].copy()
        except KeyError as e:
            raise PredictionError(f"Required feature columns not found: {e}")
        
        return prediction_data
    
    def _get_probabilities(self, predictor, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Get prediction probabilities if available."""
        try:
            if hasattr(predictor, 'predict_proba'):
                return predictor.predict_proba(data)
            else:
                return None
        except Exception as e:
            logger.warning(f"Could not get prediction probabilities: {e}")
            return None
    
    def clear_model_cache(self) -> None:
        """Clear the model cache to free memory."""
        self.loaded_models.clear()
        logger.info("Model cache cleared")
    
    def get_cached_models(self) -> List[str]:
        """Get list of cached model IDs."""
        return list(self.loaded_models.keys())
    
    def stream_predictions(
        self,
        model_info: ModelInfo,
        data: pd.DataFrame,
        batch_size: int = 1,
        delay: float = 0.0
    ):
        """
        Generator for streaming predictions.
        
        Args:
            model_info: Information about the trained model
            data: Input data for prediction
            batch_size: Number of records to predict at once
            delay: Delay between batches in seconds
            
        Yields:
            PredictionResult objects
        """
        import time
        
        try:
            predictor = self._get_predictor(model_info)
            
            for i in range(0, len(data), batch_size):
                batch_data = data.iloc[i:i+batch_size]
                
                try:
                    result = self.predict_batch(model_info, batch_data)
                    yield result
                    
                    if delay > 0:
                        time.sleep(delay)
                        
                except Exception as e:
                    logger.warning(f"Failed to predict batch {i//batch_size + 1}: {e}")
                    continue
                    
        except Exception as e:
            raise PredictionError(f"Stream prediction failed", str(e))
