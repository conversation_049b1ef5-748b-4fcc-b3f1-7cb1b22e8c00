"""
Model training module for the ML platform.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, Tuple, List
import logging
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

from autogluon.tabular import TabularPredictor

# Optional multimodal import
try:
    from autogluon.multimodal import MultiModalPredictor
    HAS_MULTIMODAL = True
except ImportError:
    MultiModalPredictor = None
    HAS_MULTIMODAL = False

from ..models.exceptions import ModelTrainingError, ConfigurationError
from ..models.config import ModelConfig
from ..models.data_models import ModelInfo, DataInfo
from ..data.utils import DataUtils

logger = logging.getLogger(__name__)


class ModelTrainer:
    """Class for training machine learning models using AutoGluon."""
    
    def __init__(self, config: Optional[ModelConfig] = None):
        self.config = config or ModelConfig()
        self.config.validate()
        
    def train_model(
        self,
        df: pd.DataFrame,
        target_column: str,
        feature_columns: Optional[List[str]] = None,
        model_path: Optional[str] = None,
        data_info: Optional[DataInfo] = None
    ) -> ModelInfo:
        """
        Train a machine learning model.
        
        Args:
            df: Training DataFrame
            target_column: Name of target column
            feature_columns: List of feature columns (all except target if None)
            model_path: Path to save model (auto-generated if None)
            data_info: Information about the dataset
            
        Returns:
            ModelInfo object with training results
            
        Raises:
            ModelTrainingError: If training fails
        """
        try:
            start_time = datetime.now()
            
            # Validate inputs
            self._validate_training_inputs(df, target_column, feature_columns)
            
            # Prepare data
            train_data = self._prepare_training_data(df, target_column, feature_columns)
            
            # Determine problem type
            problem_type = self._determine_problem_type(df[target_column])
            
            # Set up model path
            if model_path is None:
                model_path = self._generate_model_path()
            
            DataUtils.ensure_directory(Path(model_path).parent)
            
            # Choose predictor type
            use_multimodal = self._should_use_multimodal(train_data)
            
            # Train model
            if use_multimodal and HAS_MULTIMODAL:
                predictor, training_info = self._train_multimodal_model(
                    train_data, target_column, model_path
                )
                model_type = "multimodal"
            else:
                predictor, training_info = self._train_tabular_model(
                    train_data, target_column, model_path
                )
                model_type = "tabular"
            
            # Calculate training time
            training_time = (datetime.now() - start_time).total_seconds()
            
            # Get model performance
            best_model, best_score, score_metric, leaderboard = self._get_model_performance(predictor)
            
            # Create ModelInfo
            model_info = ModelInfo(
                model_id=f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                model_type=model_type,
                model_path=model_path,
                target_column=target_column,
                feature_columns=feature_columns or [col for col in df.columns if col != target_column],
                problem_type=problem_type,
                training_time=training_time,
                preset_used=self.config.preset,
                time_limit=self.config.time_limit,
                best_model=best_model,
                best_score=best_score,
                score_metric=score_metric,
                leaderboard=leaderboard,
                data_info=data_info
            )
            
            logger.info(f"Model training completed successfully in {training_time:.2f} seconds")
            logger.info(f"Best model: {best_model} with {score_metric}: {best_score:.4f}")
            
            return model_info
            
        except Exception as e:
            if isinstance(e, ModelTrainingError):
                raise
            else:
                raise ModelTrainingError(f"Model training failed", str(e))
    
    def _validate_training_inputs(
        self,
        df: pd.DataFrame,
        target_column: str,
        feature_columns: Optional[List[str]]
    ) -> None:
        """Validate training inputs."""
        if df is None or df.empty:
            raise ModelTrainingError("Training data is empty")
        
        if target_column not in df.columns:
            raise ModelTrainingError(f"Target column '{target_column}' not found in data")
        
        if feature_columns:
            missing_features = [col for col in feature_columns if col not in df.columns]
            if missing_features:
                raise ModelTrainingError(f"Feature columns not found: {missing_features}")
        
        # Check for sufficient data
        if len(df) < 10:
            raise ModelTrainingError("Insufficient training data (minimum 10 rows required)")
        
        # Check target column
        target_nulls = df[target_column].isnull().sum()
        if target_nulls > 0:
            raise ModelTrainingError(f"Target column has {target_nulls} null values")
        
        target_unique = df[target_column].nunique()
        if target_unique == 1:
            raise ModelTrainingError("Target column has only one unique value")
    
    def _prepare_training_data(
        self,
        df: pd.DataFrame,
        target_column: str,
        feature_columns: Optional[List[str]]
    ) -> pd.DataFrame:
        """Prepare data for training."""
        if feature_columns is None:
            feature_columns = [col for col in df.columns if col != target_column]
        
        # Select relevant columns
        train_data = df[feature_columns + [target_column]].copy()
        
        # Convert to pandas if needed (for cuDF compatibility)
        if hasattr(train_data, "to_pandas"):
            train_data = train_data.to_pandas()
        
        return train_data
    
    def _determine_problem_type(self, target_series: pd.Series) -> str:
        """Determine the problem type from target variable."""
        if self.config.problem_type:
            return self.config.problem_type
        
        return DataUtils.infer_problem_type(target_series)
    
    def _generate_model_path(self) -> str:
        """Generate a unique model path."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"models/autogluon_model_{timestamp}"
    
    def _should_use_multimodal(self, df: pd.DataFrame) -> bool:
        """Determine if multimodal predictor should be used."""
        if not self.config.enable_multimodal or not HAS_MULTIMODAL:
            return False
        
        # Check for image columns
        if "image" in df.columns:
            return True
        
        # Check for text columns (object dtype with long strings)
        for col in df.select_dtypes(include=['object']).columns:
            if df[col].astype(str).str.len().mean() > 50:  # Average string length > 50
                return True
        
        return False
    
    def _train_tabular_model(
        self,
        train_data: pd.DataFrame,
        target_column: str,
        model_path: str
    ) -> Tuple[TabularPredictor, Dict[str, Any]]:
        """Train a tabular model using AutoGluon."""
        try:
            # Create predictor
            predictor = TabularPredictor(
                label=target_column,
                path=model_path,
                problem_type=self.config.problem_type,
                eval_metric=None,  # Let AutoGluon choose
                verbosity=2
            )
            
            # Prepare training arguments
            fit_args = {
                "train_data": train_data,
                "presets": self.config.preset,
                "time_limit": self.config.time_limit,
                "verbosity": 2
            }
            
            # Add optional arguments
            if self.config.hyperparameters:
                fit_args["hyperparameters"] = self.config.hyperparameters
            
            if self.config.excluded_model_types:
                fit_args["excluded_model_types"] = self.config.excluded_model_types
            
            if self.config.included_model_types:
                fit_args["included_model_types"] = self.config.included_model_types
            
            # Train model
            logger.info(f"Training tabular model with preset: {self.config.preset}")
            predictor.fit(**fit_args)
            
            training_info = {
                "predictor_type": "tabular",
                "preset": self.config.preset,
                "time_limit": self.config.time_limit
            }
            
            return predictor, training_info
            
        except Exception as e:
            raise ModelTrainingError(f"Tabular model training failed", str(e))
    
    def _train_multimodal_model(
        self,
        train_data: pd.DataFrame,
        target_column: str,
        model_path: str
    ) -> Tuple[Any, Dict[str, Any]]:
        """Train a multimodal model using AutoGluon."""
        try:
            if not HAS_MULTIMODAL:
                raise ModelTrainingError("Multimodal predictor not available")
            
            # Create predictor
            predictor = MultiModalPredictor(
                label=target_column,
                path=model_path,
                problem_type=self.config.problem_type,
                verbosity=2
            )
            
            # Train model
            logger.info(f"Training multimodal model with preset: {self.config.preset}")
            predictor.fit(
                train_data=train_data,
                presets=self.config.preset,
                time_limit=self.config.time_limit
            )
            
            training_info = {
                "predictor_type": "multimodal",
                "preset": self.config.preset,
                "time_limit": self.config.time_limit
            }
            
            return predictor, training_info
            
        except Exception as e:
            raise ModelTrainingError(f"Multimodal model training failed", str(e))
    
    def _get_model_performance(self, predictor) -> Tuple[str, float, str, Optional[pd.DataFrame]]:
        """Extract model performance information."""
        try:
            # Get leaderboard
            leaderboard = predictor.leaderboard(silent=True)
            
            if leaderboard is not None and not leaderboard.empty:
                best_row = leaderboard.iloc[0]
                best_model = best_row.get('model', 'unknown')
                
                # Find the score column (usually the last numeric column)
                score_columns = leaderboard.select_dtypes(include=[np.number]).columns
                if len(score_columns) > 0:
                    score_column = score_columns[-1]  # Usually the main metric
                    best_score = best_row[score_column]
                    score_metric = score_column
                else:
                    best_score = 0.0
                    score_metric = "unknown"
                
                return best_model, best_score, score_metric, leaderboard
            else:
                return "unknown", 0.0, "unknown", None
                
        except Exception as e:
            logger.warning(f"Could not extract model performance: {e}")
            return "unknown", 0.0, "unknown", None
    
    def quick_train(
        self,
        df: pd.DataFrame,
        target_column: str,
        timeout: int = 30,
        preset: str = "medium_quality"
    ) -> Dict[str, Any]:
        """
        Quick training for ablation studies or rapid prototyping.
        
        Args:
            df: Training DataFrame
            target_column: Target column name
            timeout: Training timeout in seconds
            preset: AutoGluon preset to use
            
        Returns:
            Dictionary with performance metrics
        """
        temp_dir = tempfile.mkdtemp(prefix="quick_train_")
        
        try:
            predictor = TabularPredictor(label=target_column, path=temp_dir)
            predictor.fit(
                train_data=df,
                presets=preset,
                time_limit=timeout,
                verbosity=0
            )
            
            # Get performance
            try:
                leaderboard = predictor.leaderboard(silent=True)
                if leaderboard is not None and not leaderboard.empty:
                    return leaderboard.iloc[0].to_dict()
                else:
                    return {}
            except:
                return {}
                
        except Exception as e:
            logger.warning(f"Quick training failed: {e}")
            return {}
        finally:
            # Cleanup
            shutil.rmtree(temp_dir, ignore_errors=True)
