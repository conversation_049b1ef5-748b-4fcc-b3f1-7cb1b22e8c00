"""
Configuration classes for the ML platform.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import os


@dataclass
class AppConfig:
    """Main application configuration."""
    
    # Application settings
    app_title: str = "OpenDataAI — No-Code AI & Data Platform"
    page_layout: str = "wide"
    max_upload_size: int = 200  # MB
    
    # Directory settings
    models_dir: str = "models"
    temp_dir: str = "temp"
    logs_dir: str = "logs"
    
    # UI settings
    max_preview_rows: int = 500
    max_chart_items: int = 50
    
    # Performance settings
    enable_cudf: bool = True
    enable_multimodal: bool = True
    
    def __post_init__(self) -> None:
        """Ensure directories exist."""
        for directory in [self.models_dir, self.temp_dir, self.logs_dir]:
            Path(directory).mkdir(parents=True, exist_ok=True)


@dataclass
class PreprocessingConfig:
    """Configuration for data preprocessing."""
    
    # Imputation settings
    numeric_imputation: str = "mean"  # mean, median, constant
    categorical_imputation: str = "mode"  # mode, constant
    numeric_fill_value: float = 0.0
    categorical_fill_value: str = "__MISSING__"
    
    # Encoding settings
    enable_onehot: bool = True
    onehot_max_categories: int = 10
    enable_label_encoding: bool = False
    
    # Scaling settings
    enable_scaling: bool = False
    scaling_method: str = "standard"  # standard, minmax, robust
    
    # Feature engineering
    extract_date_parts: bool = True
    create_interaction_features: bool = False
    
    # Column selection
    selected_columns: Optional[List[str]] = None
    exclude_columns: Optional[List[str]] = None
    
    def validate(self) -> None:
        """Validate configuration parameters."""
        valid_numeric_imputation = ["mean", "median", "constant"]
        valid_categorical_imputation = ["mode", "constant"]
        valid_scaling_methods = ["standard", "minmax", "robust"]
        
        if self.numeric_imputation not in valid_numeric_imputation:
            raise ValueError(f"Invalid numeric_imputation: {self.numeric_imputation}")
        
        if self.categorical_imputation not in valid_categorical_imputation:
            raise ValueError(f"Invalid categorical_imputation: {self.categorical_imputation}")
        
        if self.scaling_method not in valid_scaling_methods:
            raise ValueError(f"Invalid scaling_method: {self.scaling_method}")


@dataclass
class ModelConfig:
    """Configuration for model training."""
    
    # AutoGluon settings
    preset: str = "medium_quality"  # medium_quality, good_quality, best_quality, extreme_quality
    time_limit: int = 300  # seconds
    
    # Model type settings
    enable_multimodal: bool = False
    problem_type: Optional[str] = None  # auto-detect if None
    
    # Training settings
    train_split: float = 0.8
    validation_split: float = 0.2
    random_state: int = 42
    
    # Advanced settings
    hyperparameters: Optional[Dict[str, Any]] = None
    excluded_model_types: Optional[List[str]] = None
    included_model_types: Optional[List[str]] = None
    
    # Resource settings
    num_cpus: Optional[int] = None
    num_gpus: Optional[int] = None
    memory_limit: Optional[str] = None
    
    def validate(self) -> None:
        """Validate configuration parameters."""
        valid_presets = ["medium_quality", "good_quality", "best_quality", "extreme_quality"]
        
        if self.preset not in valid_presets:
            raise ValueError(f"Invalid preset: {self.preset}")
        
        if not 0 < self.train_split < 1:
            raise ValueError(f"train_split must be between 0 and 1: {self.train_split}")
        
        if not 0 < self.validation_split < 1:
            raise ValueError(f"validation_split must be between 0 and 1: {self.validation_split}")
        
        if self.train_split + self.validation_split > 1:
            raise ValueError("train_split + validation_split cannot exceed 1")


@dataclass
class AblationConfig:
    """Configuration for ablation studies."""
    
    time_limit: int = 60  # seconds
    sample_fraction: float = 0.5
    components_to_test: List[str] = field(default_factory=lambda: ["numerical", "categorical"])
    quick_train_timeout: int = 30
    
    def validate(self) -> None:
        """Validate configuration parameters."""
        if not 0 < self.sample_fraction <= 1:
            raise ValueError(f"sample_fraction must be between 0 and 1: {self.sample_fraction}")
        
        if self.time_limit <= 0:
            raise ValueError(f"time_limit must be positive: {self.time_limit}")


@dataclass
class APIConfig:
    """Configuration for API server."""
    
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = False
    log_level: str = "info"
    
    # Security settings
    enable_cors: bool = True
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    max_request_size: int = 100  # MB
    
    def validate(self) -> None:
        """Validate configuration parameters."""
        if not 1000 <= self.port <= 65535:
            raise ValueError(f"Port must be between 1000 and 65535: {self.port}")
        
        if self.workers < 1:
            raise ValueError(f"workers must be at least 1: {self.workers}")
