"""
Data models for the ML platform.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
from pathlib import Path
import pandas as pd


@dataclass
class DataInfo:
    """Information about a dataset."""
    
    name: str
    shape: Tuple[int, int]
    columns: List[str]
    dtypes: Dict[str, str]
    null_counts: Dict[str, int]
    unique_counts: Dict[str, int]
    memory_usage: float  # MB
    created_at: datetime = field(default_factory=datetime.now)
    
    @classmethod
    def from_dataframe(cls, df: pd.DataFrame, name: str = "dataset") -> "DataInfo":
        """Create DataInfo from a pandas DataFrame."""
        return cls(
            name=name,
            shape=df.shape,
            columns=df.columns.tolist(),
            dtypes=df.dtypes.astype(str).to_dict(),
            null_counts=df.isnull().sum().to_dict(),
            unique_counts=df.nunique().to_dict(),
            memory_usage=df.memory_usage(deep=True).sum() / 1024 / 1024  # Convert to MB
        )
    
    @property
    def numeric_columns(self) -> List[str]:
        """Get list of numeric columns."""
        numeric_types = ['int64', 'float64', 'int32', 'float32']
        return [col for col, dtype in self.dtypes.items() if dtype in numeric_types]
    
    @property
    def categorical_columns(self) -> List[str]:
        """Get list of categorical columns."""
        return [col for col in self.columns if col not in self.numeric_columns]
    
    @property
    def has_nulls(self) -> bool:
        """Check if dataset has any null values."""
        return any(count > 0 for count in self.null_counts.values())


@dataclass
class ModelInfo:
    """Information about a trained model."""
    
    model_id: str
    model_type: str  # tabular, multimodal
    model_path: str
    target_column: str
    feature_columns: List[str]
    problem_type: str  # regression, binary, multiclass
    
    # Training information
    training_time: float  # seconds
    preset_used: str
    time_limit: int
    
    # Performance metrics
    best_model: str
    best_score: float
    score_metric: str
    leaderboard: Optional[pd.DataFrame] = None
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    data_info: Optional[DataInfo] = None
    
    @property
    def model_exists(self) -> bool:
        """Check if model files exist."""
        return Path(self.model_path).exists()
    
    def get_model_size(self) -> float:
        """Get model size in MB."""
        if not self.model_exists:
            return 0.0
        
        total_size = 0
        for path in Path(self.model_path).rglob("*"):
            if path.is_file():
                total_size += path.stat().st_size
        
        return total_size / 1024 / 1024  # Convert to MB


@dataclass
class PredictionResult:
    """Result of a prediction operation."""
    
    predictions: Union[List[Any], pd.Series]
    prediction_type: str  # single, batch, stream
    model_id: str
    
    # Metadata
    num_predictions: int
    prediction_time: float  # seconds
    created_at: datetime = field(default_factory=datetime.now)
    
    # Optional additional information
    probabilities: Optional[Union[List[List[float]], pd.DataFrame]] = None
    feature_importance: Optional[Dict[str, float]] = None
    confidence_scores: Optional[List[float]] = None
    
    @classmethod
    def from_single_prediction(
        cls, 
        prediction: Any, 
        model_id: str, 
        prediction_time: float,
        probabilities: Optional[List[float]] = None
    ) -> "PredictionResult":
        """Create PredictionResult for single prediction."""
        return cls(
            predictions=[prediction],
            prediction_type="single",
            model_id=model_id,
            num_predictions=1,
            prediction_time=prediction_time,
            probabilities=[probabilities] if probabilities else None
        )
    
    @classmethod
    def from_batch_predictions(
        cls,
        predictions: Union[List[Any], pd.Series],
        model_id: str,
        prediction_time: float,
        probabilities: Optional[pd.DataFrame] = None
    ) -> "PredictionResult":
        """Create PredictionResult for batch predictions."""
        if isinstance(predictions, pd.Series):
            pred_list = predictions.tolist()
            num_preds = len(predictions)
        else:
            pred_list = predictions
            num_preds = len(predictions)
        
        return cls(
            predictions=pred_list,
            prediction_type="batch",
            model_id=model_id,
            num_predictions=num_preds,
            prediction_time=prediction_time,
            probabilities=probabilities
        )


@dataclass
class AblationResult:
    """Result of an ablation study."""
    
    component: str
    performance_delta: float
    base_score: float
    ablated_score: float
    relative_importance: float
    
    @classmethod
    def from_scores(
        cls, 
        component: str, 
        base_score: float, 
        ablated_score: float
    ) -> "AblationResult":
        """Create AblationResult from base and ablated scores."""
        delta = base_score - ablated_score
        relative_importance = abs(delta) / abs(base_score) if base_score != 0 else 0.0
        
        return cls(
            component=component,
            performance_delta=delta,
            base_score=base_score,
            ablated_score=ablated_score,
            relative_importance=relative_importance
        )


@dataclass
class ProcessingStep:
    """Information about a data processing step."""
    
    step_name: str
    step_type: str  # imputation, encoding, scaling, feature_engineering
    parameters: Dict[str, Any]
    applied_columns: List[str]
    execution_time: float  # seconds
    created_at: datetime = field(default_factory=datetime.now)
    
    # Before/after information
    input_shape: Optional[Tuple[int, int]] = None
    output_shape: Optional[Tuple[int, int]] = None
    columns_added: Optional[List[str]] = None
    columns_removed: Optional[List[str]] = None
