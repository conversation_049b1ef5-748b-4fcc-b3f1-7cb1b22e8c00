"""
Custom exception classes for the ML platform.
"""

from typing import Optional


class MLPlatformError(Exception):
    """Base exception class for ML platform errors."""
    
    def __init__(self, message: str, details: Optional[str] = None) -> None:
        self.message = message
        self.details = details
        super().__init__(self.message)
    
    def __str__(self) -> str:
        if self.details:
            return f"{self.message}. Details: {self.details}"
        return self.message


class DataProcessingError(MLPlatformError):
    """Exception raised for data processing errors."""
    pass


class ModelTrainingError(MLPlatformError):
    """Exception raised for model training errors."""
    pass


class PredictionError(MLPlatformError):
    """Exception raised for prediction errors."""
    pass


class ConfigurationError(MLPlatformError):
    """Exception raised for configuration errors."""
    pass


class ValidationError(MLPlatformError):
    """Exception raised for data validation errors."""
    pass


class FileProcessingError(MLPlatformError):
    """Exception raised for file processing errors."""
    pass
