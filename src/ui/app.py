"""
Main Streamlit application for the ML platform.
"""

import streamlit as st
import logging
from typing import Dict, Any
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from .state_manager import StateManager
from .components import UIComponents
from ..models.config import AppConfig
from ..data.utils import DataUtils

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StreamlitApp:
    """Main Streamlit application class."""
    
    def __init__(self):
        self.state_manager = StateManager()
        self.ui_components = UIComponents()
        self.app_config = self.state_manager.get_app_config()
        
        # Page mapping
        self.pages = {
            "1. Data Ingestion": self._render_data_ingestion,
            "2. Exploration": self._render_exploration,
            "3. Preprocessing": self._render_preprocessing,
            "4. Training": self._render_training,
            "5. Evaluation": self._render_evaluation,
            "6. Ablation": self._render_ablation,
            "7. Prediction": self._render_prediction,
            "8. Export": self._render_export
        }
        
        self._setup_page_config()
    
    def _setup_page_config(self) -> None:
        """Configure Streamlit page settings."""
        st.set_page_config(
            page_title=self.app_config.app_title,
            layout=self.app_config.page_layout,
            initial_sidebar_state="expanded"
        )
    
    def run(self) -> None:
        """Run the main application."""
        try:
            self._render_header()
            self._render_sidebar()
            self._render_main_content()
            
        except Exception as e:
            logger.error(f"Application error: {e}")
            self.ui_components.display_error_message(e, "Application")
    
    def _render_header(self) -> None:
        """Render the application header."""
        st.title(self.app_config.app_title)
        st.markdown("---")
    
    def _render_sidebar(self) -> None:
        """Render the sidebar with navigation and controls."""
        with st.sidebar:
            st.header("🧭 Navigation")
            
            # Page selection
            current_page = st.radio(
                "Go to:",
                list(self.pages.keys()),
                index=0
            )
            st.session_state.current_page = current_page
            
            st.markdown("---")
            
            # System information
            self._render_system_info()
            
            st.markdown("---")
            
            # Reset controls
            self._render_reset_controls()
            
            st.markdown("---")
            
            # State summary
            self._render_state_summary()
    
    def _render_system_info(self) -> None:
        """Render system information."""
        st.subheader("🔧 System Info")
        
        # Check for optional dependencies
        cudf_available = DataUtils.check_cudf_availability()
        
        try:
            from autogluon.multimodal import MultiModalPredictor
            multimodal_available = True
        except ImportError:
            multimodal_available = False
        
        st.write(f"**RAPIDS/cuDF:** {'✅' if cudf_available else '❌'}")
        st.write(f"**Multimodal:** {'✅' if multimodal_available else '❌'}")
    
    def _render_reset_controls(self) -> None:
        """Render reset controls."""
        st.subheader("🔄 Reset Options")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("Reset Data", help="Clear all data but keep models"):
                self.state_manager.clear_all_data()
                st.success("Data cleared!")
                st.rerun()
        
        with col2:
            if st.button("Reset All", help="Clear everything including models"):
                self.state_manager.clear_all_data()
                self.state_manager.clear_model_data()
                # Also cleanup model directories
                DataUtils.cleanup_directory(self.app_config.models_dir)
                st.success("Everything cleared!")
                st.rerun()
    
    def _render_state_summary(self) -> None:
        """Render current state summary."""
        st.subheader("📊 Current State")
        
        summary = self.state_manager.get_state_summary()
        
        # Data status
        if summary['has_raw_data']:
            st.write(f"✅ Raw Data: {summary['raw_data_shape']}")
        else:
            st.write("❌ No raw data")
        
        if summary['has_processed_data']:
            st.write(f"✅ Processed Data: {summary['processed_data_shape']}")
        else:
            st.write("❌ No processed data")
        
        # Model status
        if summary['has_model']:
            st.write(f"✅ Model: {summary['model_type']}")
        else:
            st.write("❌ No trained model")
        
        # Additional info
        if summary['processing_steps_count'] > 0:
            st.write(f"🔧 Processing steps: {summary['processing_steps_count']}")
        
        if summary['has_image_dir']:
            st.write("🖼️ Images extracted")
        
        if summary['has_api_process']:
            st.write("🚀 API server running")
    
    def _render_main_content(self) -> None:
        """Render the main content area."""
        current_page = st.session_state.current_page
        
        if current_page in self.pages:
            self.pages[current_page]()
        else:
            st.error(f"Unknown page: {current_page}")
    
    # Page rendering methods (placeholders - will be implemented in separate page files)
    def _render_data_ingestion(self) -> None:
        """Render data ingestion page."""
        from .pages.data_ingestion import DataIngestionPage
        page = DataIngestionPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_exploration(self) -> None:
        """Render data exploration page."""
        from .pages.exploration import ExplorationPage
        page = ExplorationPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_preprocessing(self) -> None:
        """Render preprocessing page."""
        from .pages.preprocessing import PreprocessingPage
        page = PreprocessingPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_training(self) -> None:
        """Render training page."""
        from .pages.training import TrainingPage
        page = TrainingPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_evaluation(self) -> None:
        """Render evaluation page."""
        from .pages.evaluation import EvaluationPage
        page = EvaluationPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_ablation(self) -> None:
        """Render ablation page."""
        from .pages.ablation import AblationPage
        page = AblationPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_prediction(self) -> None:
        """Render prediction page."""
        from .pages.prediction import PredictionPage
        page = PredictionPage(self.state_manager, self.ui_components)
        page.render()
    
    def _render_export(self) -> None:
        """Render export page."""
        from .pages.export import ExportPage
        page = ExportPage(self.state_manager, self.ui_components)
        page.render()


def main():
    """Main entry point for the Streamlit application."""
    try:
        app = StreamlitApp()
        app.run()
    except Exception as e:
        st.error(f"Failed to start application: {e}")
        logger.error(f"Application startup failed: {e}")


if __name__ == "__main__":
    main()
