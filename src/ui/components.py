"""
Reusable UI components for the Streamlit application.
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List, Tuple, Union
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from ..models.data_models import DataInfo, ModelInfo
from ..data.utils import DataUtils


class UIComponents:
    """Collection of reusable UI components."""
    
    @staticmethod
    def display_data_preview(
        df: pd.DataFrame, 
        title: str = "Data Preview",
        max_rows: int = 500
    ) -> None:
        """Display a data preview with sampling information."""
        if df is None or df.empty:
            st.warning("No data to display")
            return
        
        preview_df, message = DataUtils.safe_preview(df, max_rows)
        
        st.subheader(title)
        st.info(message)
        st.dataframe(preview_df, use_container_width=True)
    
    @staticmethod
    def display_data_info(data_info: DataInfo) -> None:
        """Display comprehensive data information."""
        if data_info is None:
            st.warning("No data information available")
            return
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Rows", f"{data_info.shape[0]:,}")
        with col2:
            st.metric("Columns", data_info.shape[1])
        with col3:
            st.metric("Memory (MB)", f"{data_info.memory_usage:.2f}")
        with col4:
            st.metric("Null Values", "Yes" if data_info.has_nulls else "No")
        
        # Column type breakdown
        st.subheader("Column Information")
        
        col_info = pd.DataFrame({
            'Column': data_info.columns,
            'Type': [data_info.dtypes[col] for col in data_info.columns],
            'Nulls': [data_info.null_counts[col] for col in data_info.columns],
            'Unique': [data_info.unique_counts[col] for col in data_info.columns]
        })
        
        st.dataframe(col_info, use_container_width=True)
    
    @staticmethod
    def display_column_statistics(df: pd.DataFrame) -> None:
        """Display detailed column statistics."""
        if df is None or df.empty:
            return
        
        st.subheader("Column Statistics")
        
        # Numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            st.write("**Numeric Columns:**")
            numeric_stats = df[numeric_cols].describe()
            st.dataframe(numeric_stats, use_container_width=True)
        
        # Categorical columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            st.write("**Categorical Columns:**")
            cat_stats = []
            for col in categorical_cols:
                stats = {
                    'Column': col,
                    'Unique Values': df[col].nunique(),
                    'Most Frequent': df[col].mode().iloc[0] if not df[col].mode().empty else 'N/A',
                    'Frequency': df[col].value_counts().iloc[0] if len(df[col].value_counts()) > 0 else 0
                }
                cat_stats.append(stats)
            
            st.dataframe(pd.DataFrame(cat_stats), use_container_width=True)
    
    @staticmethod
    def create_distribution_plots(df: pd.DataFrame, max_cols: int = 6) -> None:
        """Create distribution plots for numeric columns."""
        numeric_cols = df.select_dtypes(include=[np.number]).columns[:max_cols]
        
        if len(numeric_cols) == 0:
            st.info("No numeric columns available for distribution plots")
            return
        
        st.subheader("Distribution Plots")
        
        # Create subplots
        n_cols = min(2, len(numeric_cols))
        n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
        
        fig = make_subplots(
            rows=n_rows, 
            cols=n_cols,
            subplot_titles=numeric_cols,
            vertical_spacing=0.1
        )
        
        for i, col in enumerate(numeric_cols):
            row = i // n_cols + 1
            col_pos = i % n_cols + 1
            
            # Create histogram
            fig.add_trace(
                go.Histogram(x=df[col], name=col, showlegend=False),
                row=row, col=col_pos
            )
        
        fig.update_layout(height=300 * n_rows, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_correlation_heatmap(df: pd.DataFrame) -> None:
        """Create correlation heatmap for numeric columns."""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) < 2:
            st.info("Need at least 2 numeric columns for correlation analysis")
            return
        
        st.subheader("Correlation Heatmap")
        
        corr_matrix = df[numeric_cols].corr()
        
        fig = px.imshow(
            corr_matrix,
            text_auto=True,
            aspect="auto",
            color_continuous_scale="RdBu_r",
            title="Feature Correlation Matrix"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def display_model_info(model_info: ModelInfo) -> None:
        """Display model information."""
        if model_info is None:
            st.warning("No model information available")
            return
        
        st.subheader("Model Information")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Model Type", model_info.model_type.title())
        with col2:
            st.metric("Problem Type", model_info.problem_type.title())
        with col3:
            st.metric("Training Time", f"{model_info.training_time:.1f}s")
        with col4:
            st.metric("Best Score", f"{model_info.best_score:.4f}")
        
        # Additional details
        with st.expander("Model Details"):
            st.write(f"**Model ID:** {model_info.model_id}")
            st.write(f"**Model Path:** {model_info.model_path}")
            st.write(f"**Target Column:** {model_info.target_column}")
            st.write(f"**Feature Columns:** {len(model_info.feature_columns)}")
            st.write(f"**Best Model:** {model_info.best_model}")
            st.write(f"**Score Metric:** {model_info.score_metric}")
            st.write(f"**Preset Used:** {model_info.preset_used}")
            st.write(f"**Time Limit:** {model_info.time_limit}s")
            st.write(f"**Created:** {model_info.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    @staticmethod
    def display_leaderboard(leaderboard: pd.DataFrame, max_rows: int = 20) -> None:
        """Display model leaderboard."""
        if leaderboard is None or leaderboard.empty:
            st.info("No leaderboard data available")
            return
        
        st.subheader("Model Leaderboard")
        
        # Display top models
        display_df = leaderboard.head(max_rows)
        st.dataframe(display_df, use_container_width=True)
        
        if len(leaderboard) > max_rows:
            st.info(f"Showing top {max_rows} models out of {len(leaderboard)} total")
    
    @staticmethod
    def display_feature_importance(
        importance_df: pd.DataFrame, 
        max_features: int = 20,
        chart_type: str = "bar"
    ) -> None:
        """Display feature importance."""
        if importance_df is None or importance_df.empty:
            st.info("No feature importance data available")
            return
        
        st.subheader("Feature Importance")
        
        # Ensure we have the right columns
        if 'importance' not in importance_df.columns:
            st.warning("Feature importance data format not recognized")
            st.dataframe(importance_df)
            return
        
        # Get top features
        top_features = importance_df.head(max_features)
        
        if chart_type == "bar":
            fig = px.bar(
                top_features,
                x='importance',
                y=top_features.index,
                orientation='h',
                title=f"Top {len(top_features)} Most Important Features"
            )
            fig.update_layout(yaxis={'categoryorder': 'total ascending'})
        else:
            fig = px.scatter(
                top_features,
                x=range(len(top_features)),
                y='importance',
                hover_data=[top_features.index],
                title=f"Feature Importance (Top {len(top_features)})"
            )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Also show as table
        with st.expander("Feature Importance Table"):
            st.dataframe(top_features, use_container_width=True)
    
    @staticmethod
    def create_metric_comparison_chart(
        metrics_data: List[Dict[str, Any]], 
        metric_name: str,
        title: str = "Metric Comparison"
    ) -> None:
        """Create a comparison chart for metrics."""
        if not metrics_data:
            st.info("No metrics data available")
            return
        
        # Extract data for plotting
        labels = [item.get('label', f"Item {i}") for i, item in enumerate(metrics_data)]
        values = [item.get(metric_name, 0) for item in metrics_data]
        
        fig = px.bar(
            x=labels,
            y=values,
            title=title,
            labels={'x': 'Models/Components', 'y': metric_name}
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def display_processing_steps(steps: List[Any]) -> None:
        """Display data processing steps."""
        if not steps:
            st.info("No processing steps recorded")
            return
        
        st.subheader("Data Processing Steps")
        
        for i, step in enumerate(steps, 1):
            with st.expander(f"Step {i}: {step.step_name}"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Type:** {step.step_type}")
                    st.write(f"**Execution Time:** {step.execution_time:.3f}s")
                    st.write(f"**Applied Columns:** {len(step.applied_columns)}")
                
                with col2:
                    if step.input_shape and step.output_shape:
                        st.write(f"**Input Shape:** {step.input_shape}")
                        st.write(f"**Output Shape:** {step.output_shape}")
                    
                    if step.columns_added:
                        st.write(f"**Columns Added:** {len(step.columns_added)}")
                    if step.columns_removed:
                        st.write(f"**Columns Removed:** {len(step.columns_removed)}")
                
                if step.parameters:
                    st.write("**Parameters:**")
                    st.json(step.parameters)
    
    @staticmethod
    def create_progress_indicator(current_step: int, total_steps: int, step_names: List[str]) -> None:
        """Create a progress indicator for the workflow."""
        st.subheader("Workflow Progress")
        
        progress = current_step / total_steps
        st.progress(progress)
        
        # Show step indicators
        cols = st.columns(total_steps)
        for i, (col, step_name) in enumerate(zip(cols, step_names)):
            with col:
                if i < current_step:
                    st.success(f"✅ {step_name}")
                elif i == current_step:
                    st.info(f"🔄 {step_name}")
                else:
                    st.write(f"⏳ {step_name}")
    
    @staticmethod
    def display_error_message(error: Exception, context: str = "") -> None:
        """Display a formatted error message."""
        st.error(f"**Error{' in ' + context if context else ''}:** {str(error)}")
        
        with st.expander("Error Details"):
            st.code(str(error))
            if hasattr(error, 'details') and error.details:
                st.write("**Additional Details:**")
                st.write(error.details)
    
    @staticmethod
    def display_success_message(message: str, details: Optional[str] = None) -> None:
        """Display a formatted success message."""
        st.success(message)
        
        if details:
            with st.expander("Details"):
                st.write(details)
