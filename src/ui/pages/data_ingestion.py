"""
Data ingestion page for the Streamlit application.
"""

import streamlit as st
import pandas as pd
from typing import Optional
import logging

from ...data.ingestion import DataIngestionManager
from ...models.exceptions import DataProcessingError, FileProcessingError
from ..state_manager import StateManager
from ..components import UIComponents

logger = logging.getLogger(__name__)


class DataIngestionPage:
    """Data ingestion page implementation."""
    
    def __init__(self, state_manager: StateManager, ui_components: UIComponents):
        self.state_manager = state_manager
        self.ui_components = ui_components
        self.ingestion_manager = DataIngestionManager()
    
    def render(self) -> None:
        """Render the data ingestion page."""
        st.header("📁 Step 1 — Data Ingestion")
        st.markdown("Upload your dataset or use a demo dataset to get started.")
        
        # Create tabs for different ingestion methods
        tab1, tab2, tab3 = st.tabs(["📤 Upload File", "🎯 Demo Datasets", "🔗 URL/Path"])
        
        with tab1:
            self._render_file_upload()
        
        with tab2:
            self._render_demo_datasets()
        
        with tab3:
            self._render_url_input()
        
        # Display current data if available
        self._display_current_data()
    
    def _render_file_upload(self) -> None:
        """Render file upload interface."""
        st.subheader("Upload Dataset")
        
        # Get supported formats
        supported_formats = self.ingestion_manager.get_supported_formats()
        format_list = ", ".join(supported_formats.keys())
        
        st.info(f"Supported formats: {format_list}")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=list(supported_formats.keys()),
            help="Upload your dataset file. Supported formats include CSV, JSON, Excel, Parquet, and ZIP (for images)."
        )
        
        if uploaded_file is not None:
            try:
                with st.spinner("Loading data..."):
                    # Load the data
                    df, data_info = self.ingestion_manager.load_data(
                        uploaded_file,
                        file_format=None  # Auto-detect
                    )
                    
                    # Handle special case for ZIP files (image datasets)
                    if uploaded_file.name.lower().endswith('.zip'):
                        self._handle_zip_upload(df, uploaded_file.name)
                    else:
                        # Store in state
                        self.state_manager.set_raw_data(df, data_info)
                        
                        # Show success message
                        self.ui_components.display_success_message(
                            f"Successfully loaded {uploaded_file.name}",
                            f"Shape: {df.shape[0]:,} rows × {df.shape[1]} columns"
                        )
                
            except (DataProcessingError, FileProcessingError) as e:
                self.ui_components.display_error_message(e, "File Upload")
            except Exception as e:
                self.ui_components.display_error_message(
                    Exception(f"Unexpected error loading file: {str(e)}"),
                    "File Upload"
                )
    
    def _render_demo_datasets(self) -> None:
        """Render demo dataset selection."""
        st.subheader("Demo Datasets")
        st.markdown("Select a demo dataset to explore the platform features.")
        
        # Demo dataset options
        demo_datasets = {
            "california_housing": {
                "name": "California Housing",
                "description": "Regression dataset with house prices in California",
                "size": "20,640 rows × 9 columns",
                "target": "MedHouseVal"
            },
            "iris": {
                "name": "Iris Flowers",
                "description": "Classic classification dataset with flower species",
                "size": "150 rows × 5 columns",
                "target": "target"
            },
            "wine": {
                "name": "Wine Quality",
                "description": "Classification dataset with wine quality ratings",
                "size": "178 rows × 14 columns",
                "target": "target"
            },
            "breast_cancer": {
                "name": "Breast Cancer",
                "description": "Binary classification for cancer diagnosis",
                "size": "569 rows × 31 columns",
                "target": "target"
            }
        }
        
        # Display dataset options
        for dataset_key, dataset_info in demo_datasets.items():
            with st.expander(f"📊 {dataset_info['name']}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.write(f"**Description:** {dataset_info['description']}")
                    st.write(f"**Size:** {dataset_info['size']}")
                    st.write(f"**Target:** {dataset_info['target']}")
                
                with col2:
                    if st.button(f"Load", key=f"load_{dataset_key}"):
                        try:
                            with st.spinner(f"Loading {dataset_info['name']}..."):
                                df, data_info = self.ingestion_manager.load_demo_dataset(dataset_key)
                                
                                # Store in state
                                self.state_manager.set_raw_data(df, data_info)
                                
                                # Show success message
                                self.ui_components.display_success_message(
                                    f"Successfully loaded {dataset_info['name']} dataset",
                                    f"Shape: {df.shape[0]:,} rows × {df.shape[1]} columns"
                                )
                                
                                st.rerun()
                        
                        except Exception as e:
                            self.ui_components.display_error_message(e, "Demo Dataset Loading")
    
    def _render_url_input(self) -> None:
        """Render URL/path input interface."""
        st.subheader("Load from URL or File Path")
        st.markdown("Load data from a URL or local file path.")
        
        # URL/Path input
        data_source = st.text_input(
            "Enter URL or file path:",
            placeholder="https://example.com/data.csv or /path/to/file.csv",
            help="Enter a URL to a CSV file or a local file path"
        )
        
        # Format selection
        format_options = ["auto-detect"] + list(self.ingestion_manager.get_supported_formats().keys())
        selected_format = st.selectbox(
            "File format:",
            format_options,
            help="Select the file format or use auto-detect"
        )
        
        if st.button("Load Data", disabled=not data_source):
            try:
                with st.spinner("Loading data from source..."):
                    file_format = None if selected_format == "auto-detect" else selected_format
                    
                    df, data_info = self.ingestion_manager.load_data(
                        data_source,
                        file_format=file_format
                    )
                    
                    # Store in state
                    self.state_manager.set_raw_data(df, data_info)
                    
                    # Show success message
                    self.ui_components.display_success_message(
                        f"Successfully loaded data from {data_source}",
                        f"Shape: {df.shape[0]:,} rows × {df.shape[1]} columns"
                    )
                    
                    st.rerun()
            
            except Exception as e:
                self.ui_components.display_error_message(e, "URL/Path Loading")
    
    def _handle_zip_upload(self, files_df: pd.DataFrame, filename: str) -> None:
        """Handle ZIP file upload for image datasets."""
        if 'extraction_dir' in files_df.attrs:
            extraction_dir = files_df.attrs['extraction_dir']
            self.state_manager.set_image_directory(extraction_dir)
            
            st.success(f"Extracted {len(files_df)} files from {filename}")
            st.info(
                "ZIP file extracted successfully! The files are available for multimodal training. "
                "You can map image filenames to your dataset in the Preprocessing step."
            )
            
            # Show extracted files preview
            with st.expander("View extracted files"):
                st.dataframe(files_df.head(20), use_container_width=True)
                if len(files_df) > 20:
                    st.info(f"Showing first 20 files out of {len(files_df)} total")
        else:
            st.warning("Could not extract files from ZIP archive")
    
    def _display_current_data(self) -> None:
        """Display currently loaded data."""
        current_data = self.state_manager.get_raw_data()
        data_info = self.state_manager.get_data_info()
        
        if current_data is not None:
            st.markdown("---")
            st.subheader("📋 Current Dataset")
            
            # Display data info
            if data_info:
                self.ui_components.display_data_info(data_info)
            
            # Display data preview
            self.ui_components.display_data_preview(
                current_data,
                title="Data Preview",
                max_rows=self.state_manager.get_app_config().max_preview_rows
            )
            
            # Additional actions
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("🔄 Refresh Preview"):
                    st.rerun()
            
            with col2:
                if st.button("📊 View Statistics"):
                    with st.expander("Dataset Statistics", expanded=True):
                        self.ui_components.display_column_statistics(current_data)
            
            with col3:
                if st.button("🗑️ Clear Data"):
                    self.state_manager.clear_all_data()
                    st.success("Data cleared!")
                    st.rerun()
        
        else:
            st.info("👆 Upload a dataset or select a demo dataset to get started!")
            
            # Show helpful tips
            with st.expander("💡 Tips for Data Ingestion"):
                st.markdown("""
                **Supported File Formats:**
                - **CSV**: Comma-separated values (most common)
                - **JSON**: JavaScript Object Notation
                - **Excel**: .xlsx files
                - **Parquet**: Columnar storage format
                - **ZIP**: For image datasets (extracts files for multimodal learning)
                
                **Best Practices:**
                - Ensure your data has clear column headers
                - Check for missing values and data quality issues
                - For large datasets, consider using Parquet format for better performance
                - For image datasets, organize images in a ZIP file with a CSV containing filenames
                
                **Demo Datasets:**
                - Use demo datasets to explore platform features
                - Each demo dataset is designed for different ML tasks
                - Perfect for learning and testing workflows
                """)
