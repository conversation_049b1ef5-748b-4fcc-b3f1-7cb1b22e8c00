"""
Data exploration page for the Streamlit application.
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Optional, List
import logging

from ..state_manager import StateManager
from ..components import UIComponents
from ...data.utils import DataUtils

logger = logging.getLogger(__name__)


class ExplorationPage:
    """Data exploration page implementation."""
    
    def __init__(self, state_manager: StateManager, ui_components: UIComponents):
        self.state_manager = state_manager
        self.ui_components = ui_components
    
    def render(self) -> None:
        """Render the data exploration page."""
        st.header("🔍 Step 2 — Data Exploration")
        st.markdown("Analyze your dataset to understand its structure, quality, and characteristics.")
        
        # Check if data is available
        current_data = self.state_manager.get_current_data()
        
        if current_data is None:
            st.warning("⚠️ No data available. Please upload data in Step 1 first.")
            self._render_no_data_help()
            return
        
        # Create tabs for different exploration views
        tab1, tab2, tab3, tab4 = st.tabs([
            "📊 Overview", 
            "📈 Distributions", 
            "🔗 Correlations", 
            "🔍 Data Quality"
        ])
        
        with tab1:
            self._render_overview(current_data)
        
        with tab2:
            self._render_distributions(current_data)
        
        with tab3:
            self._render_correlations(current_data)
        
        with tab4:
            self._render_data_quality(current_data)
    
    def _render_no_data_help(self) -> None:
        """Render help information when no data is available."""
        st.info("👆 Go to **Step 1: Data Ingestion** to upload your dataset or load a demo dataset.")
        
        with st.expander("💡 What you can do in Data Exploration"):
            st.markdown("""
            **Data Overview:**
            - View dataset shape, column types, and memory usage
            - Examine sample data and basic statistics
            - Identify potential target variables
            
            **Distribution Analysis:**
            - Visualize numeric feature distributions
            - Analyze categorical variable frequencies
            - Detect outliers and anomalies
            
            **Correlation Analysis:**
            - Explore relationships between features
            - Identify highly correlated variables
            - Understand feature interactions
            
            **Data Quality Assessment:**
            - Check for missing values
            - Identify duplicate records
            - Assess data consistency
            """)
    
    def _render_overview(self, df: pd.DataFrame) -> None:
        """Render dataset overview."""
        st.subheader("📋 Dataset Overview")
        
        # Basic metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Rows", f"{len(df):,}")
        with col2:
            st.metric("Total Columns", len(df.columns))
        with col3:
            memory_mb = df.memory_usage(deep=True).sum() / 1024 / 1024
            st.metric("Memory Usage", f"{memory_mb:.1f} MB")
        with col4:
            null_count = df.isnull().sum().sum()
            st.metric("Null Values", f"{null_count:,}")
        
        # Column information
        st.subheader("📝 Column Information")
        
        # Get column types
        column_types = DataUtils.get_column_types(df)
        
        col_info_data = []
        for col in df.columns:
            col_type = "Numeric" if col in column_types['numeric'] else \
                      "Categorical" if col in column_types['categorical'] else \
                      "DateTime" if col in column_types['datetime'] else \
                      "Boolean" if col in column_types['boolean'] else "Other"
            
            col_info_data.append({
                'Column': col,
                'Type': col_type,
                'Dtype': str(df[col].dtype),
                'Non-Null': f"{df[col].count():,}",
                'Null %': f"{(df[col].isnull().sum() / len(df) * 100):.1f}%",
                'Unique': f"{df[col].nunique():,}",
                'Memory (KB)': f"{df[col].memory_usage(deep=True) / 1024:.1f}"
            })
        
        col_info_df = pd.DataFrame(col_info_data)
        st.dataframe(col_info_df, use_container_width=True)
        
        # Sample data
        st.subheader("👀 Sample Data")
        
        col1, col2 = st.columns([3, 1])
        with col1:
            sample_size = st.slider("Sample size", 5, min(50, len(df)), 10)
        with col2:
            if st.button("🎲 Random Sample"):
                st.rerun()
        
        sample_df = df.sample(n=sample_size, random_state=42) if len(df) >= sample_size else df
        st.dataframe(sample_df, use_container_width=True)
        
        # Basic statistics
        if len(column_types['numeric']) > 0:
            st.subheader("📊 Numeric Statistics")
            numeric_stats = df[column_types['numeric']].describe()
            st.dataframe(numeric_stats, use_container_width=True)
    
    def _render_distributions(self, df: pd.DataFrame) -> None:
        """Render distribution analysis."""
        st.subheader("📈 Feature Distributions")
        
        column_types = DataUtils.get_column_types(df)
        
        # Numeric distributions
        if column_types['numeric']:
            st.write("**Numeric Features:**")
            
            # Column selection
            selected_numeric = st.multiselect(
                "Select numeric columns to visualize:",
                column_types['numeric'],
                default=column_types['numeric'][:4]  # Default to first 4
            )
            
            if selected_numeric:
                # Create distribution plots
                self.ui_components.create_distribution_plots(
                    df[selected_numeric], 
                    max_cols=len(selected_numeric)
                )
                
                # Outlier detection
                with st.expander("🎯 Outlier Detection"):
                    outlier_method = st.selectbox(
                        "Detection method:",
                        ["IQR", "Z-Score"],
                        help="IQR: Interquartile Range method, Z-Score: Standard deviation method"
                    )
                    
                    threshold = st.slider(
                        "Threshold:",
                        1.0, 5.0, 1.5 if outlier_method == "IQR" else 3.0,
                        step=0.1
                    )
                    
                    for col in selected_numeric:
                        method = outlier_method.lower().replace('-', '')
                        outliers = DataUtils.detect_outliers(df[col], method=method, threshold=threshold)
                        outlier_count = outliers.sum()
                        outlier_pct = (outlier_count / len(df)) * 100
                        
                        st.write(f"**{col}:** {outlier_count} outliers ({outlier_pct:.1f}%)")
        
        # Categorical distributions
        if column_types['categorical']:
            st.write("**Categorical Features:**")
            
            selected_categorical = st.selectbox(
                "Select categorical column:",
                column_types['categorical']
            )
            
            if selected_categorical:
                # Value counts
                value_counts = df[selected_categorical].value_counts().head(20)
                
                # Create bar chart
                import plotly.express as px
                fig = px.bar(
                    x=value_counts.index,
                    y=value_counts.values,
                    title=f"Distribution of {selected_categorical}",
                    labels={'x': selected_categorical, 'y': 'Count'}
                )
                fig.update_xaxes(tickangle=45)
                st.plotly_chart(fig, use_container_width=True)
                
                # Show statistics
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Unique Values", df[selected_categorical].nunique())
                with col2:
                    st.metric("Most Frequent", value_counts.index[0] if len(value_counts) > 0 else "N/A")
                with col3:
                    st.metric("Frequency", value_counts.iloc[0] if len(value_counts) > 0 else 0)
    
    def _render_correlations(self, df: pd.DataFrame) -> None:
        """Render correlation analysis."""
        st.subheader("🔗 Correlation Analysis")
        
        column_types = DataUtils.get_column_types(df)
        
        if len(column_types['numeric']) < 2:
            st.info("Need at least 2 numeric columns for correlation analysis.")
            return
        
        # Correlation heatmap
        self.ui_components.create_correlation_heatmap(df[column_types['numeric']])
        
        # High correlation pairs
        st.subheader("🔍 High Correlation Pairs")
        
        correlation_threshold = st.slider(
            "Correlation threshold:",
            0.5, 0.95, 0.8,
            help="Show pairs with correlation above this threshold"
        )
        
        corr_matrix = df[column_types['numeric']].corr()
        
        # Find high correlation pairs
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_value = corr_matrix.iloc[i, j]
                if abs(corr_value) >= correlation_threshold:
                    high_corr_pairs.append({
                        'Feature 1': corr_matrix.columns[i],
                        'Feature 2': corr_matrix.columns[j],
                        'Correlation': corr_value,
                        'Abs Correlation': abs(corr_value)
                    })
        
        if high_corr_pairs:
            high_corr_df = pd.DataFrame(high_corr_pairs)
            high_corr_df = high_corr_df.sort_values('Abs Correlation', ascending=False)
            st.dataframe(high_corr_df, use_container_width=True)
            
            if len(high_corr_pairs) > 0:
                st.warning(
                    f"Found {len(high_corr_pairs)} highly correlated feature pairs. "
                    "Consider removing redundant features during preprocessing."
                )
        else:
            st.info(f"No feature pairs with correlation above {correlation_threshold:.1f}")
    
    def _render_data_quality(self, df: pd.DataFrame) -> None:
        """Render data quality assessment."""
        st.subheader("🔍 Data Quality Assessment")
        
        # Missing values analysis
        st.write("**Missing Values Analysis:**")
        
        missing_data = df.isnull().sum()
        missing_pct = (missing_data / len(df)) * 100
        
        missing_df = pd.DataFrame({
            'Column': missing_data.index,
            'Missing Count': missing_data.values,
            'Missing %': missing_pct.values
        })
        missing_df = missing_df[missing_df['Missing Count'] > 0].sort_values('Missing %', ascending=False)
        
        if not missing_df.empty:
            st.dataframe(missing_df, use_container_width=True)
            
            # Visualize missing data pattern
            if len(missing_df) > 1:
                import plotly.express as px
                fig = px.bar(
                    missing_df,
                    x='Column',
                    y='Missing %',
                    title="Missing Data by Column"
                )
                fig.update_xaxes(tickangle=45)
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.success("✅ No missing values found!")
        
        # Duplicate records
        st.write("**Duplicate Records:**")
        
        duplicate_count = df.duplicated().sum()
        duplicate_pct = (duplicate_count / len(df)) * 100
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Duplicate Rows", f"{duplicate_count:,}")
        with col2:
            st.metric("Duplicate %", f"{duplicate_pct:.2f}%")
        
        if duplicate_count > 0:
            st.warning(f"Found {duplicate_count} duplicate rows ({duplicate_pct:.2f}%)")
            
            if st.button("👀 Show Duplicate Rows"):
                duplicates = df[df.duplicated(keep=False)].sort_values(df.columns.tolist())
                st.dataframe(duplicates.head(20), use_container_width=True)
        else:
            st.success("✅ No duplicate rows found!")
        
        # Data consistency checks
        st.write("**Data Consistency:**")
        
        consistency_issues = []
        
        # Check for mixed data types in object columns
        for col in df.select_dtypes(include=['object']).columns:
            sample_types = df[col].dropna().apply(type).unique()
            if len(sample_types) > 1:
                consistency_issues.append(f"Column '{col}' has mixed data types")
        
        # Check for unusual string patterns
        for col in df.select_dtypes(include=['object']).columns:
            if df[col].astype(str).str.len().std() > 50:  # High variation in string length
                consistency_issues.append(f"Column '{col}' has highly variable string lengths")
        
        if consistency_issues:
            for issue in consistency_issues:
                st.warning(f"⚠️ {issue}")
        else:
            st.success("✅ No obvious consistency issues found!")
        
        # Summary recommendations
        with st.expander("💡 Data Quality Recommendations"):
            recommendations = []
            
            if duplicate_count > 0:
                recommendations.append("Consider removing duplicate rows during preprocessing")
            
            if not missing_df.empty:
                high_missing = missing_df[missing_df['Missing %'] > 50]
                if not high_missing.empty:
                    recommendations.append("Consider dropping columns with >50% missing values")
                else:
                    recommendations.append("Apply appropriate imputation strategies for missing values")
            
            if consistency_issues:
                recommendations.append("Review and clean inconsistent data formats")
            
            if not recommendations:
                recommendations.append("Data quality looks good! Ready for preprocessing and modeling.")
            
            for i, rec in enumerate(recommendations, 1):
                st.write(f"{i}. {rec}")
