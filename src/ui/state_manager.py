"""
State management for the Streamlit application.
"""

import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime

from ..models.data_models import DataInfo, ModelInfo, ProcessingStep
from ..models.config import AppConfig, PreprocessingConfig, ModelConfig

logger = logging.getLogger(__name__)


class StateManager:
    """Manages application state across Streamlit sessions."""
    
    def __init__(self):
        self._initialize_state()
    
    def _initialize_state(self) -> None:
        """Initialize session state with default values."""
        defaults = {
            # Data-related state
            'raw_data': None,
            'processed_data': None,
            'data_info': None,
            'processing_steps': [],
            'image_dir': None,
            
            # Model-related state
            'model_info': None,
            'predictor_path': None,
            'predictor_type': None,
            
            # Configuration state
            'app_config': AppConfig(),
            'preprocessing_config': PreprocessingConfig(),
            'model_config': ModelConfig(),
            
            # UI state
            'current_page': "1. Data Ingestion",
            'last_batch_predictions': None,
            'api_proc': None,
            
            # Cache for expensive operations
            'feature_importance_cache': {},
            'leaderboard_cache': {},
            'evaluation_cache': {}
        }
        
        for key, value in defaults.items():
            if key not in st.session_state:
                st.session_state[key] = value
    
    # Data management methods
    def set_raw_data(self, df: pd.DataFrame, data_info: Optional[DataInfo] = None) -> None:
        """Set raw data and associated information."""
        st.session_state.raw_data = df
        st.session_state.data_info = data_info or DataInfo.from_dataframe(df)
        logger.info(f"Raw data set: {df.shape}")
    
    def get_raw_data(self) -> Optional[pd.DataFrame]:
        """Get raw data."""
        return st.session_state.get('raw_data')
    
    def set_processed_data(self, df: pd.DataFrame, processing_steps: List[ProcessingStep]) -> None:
        """Set processed data and processing steps."""
        st.session_state.processed_data = df
        st.session_state.processing_steps = processing_steps
        logger.info(f"Processed data set: {df.shape}")
    
    def get_processed_data(self) -> Optional[pd.DataFrame]:
        """Get processed data."""
        return st.session_state.get('processed_data')
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """Get the most recent data (processed if available, otherwise raw)."""
        processed = self.get_processed_data()
        if processed is not None:
            return processed
        return self.get_raw_data()
    
    def get_data_info(self) -> Optional[DataInfo]:
        """Get data information."""
        return st.session_state.get('data_info')
    
    def set_image_directory(self, image_dir: str) -> None:
        """Set image directory for multimodal datasets."""
        st.session_state.image_dir = image_dir
        logger.info(f"Image directory set: {image_dir}")
    
    def get_image_directory(self) -> Optional[str]:
        """Get image directory."""
        return st.session_state.get('image_dir')
    
    # Model management methods
    def set_model_info(self, model_info: ModelInfo) -> None:
        """Set model information."""
        st.session_state.model_info = model_info
        st.session_state.predictor_path = model_info.model_path
        st.session_state.predictor_type = model_info.model_type
        logger.info(f"Model info set: {model_info.model_id}")
    
    def get_model_info(self) -> Optional[ModelInfo]:
        """Get model information."""
        return st.session_state.get('model_info')
    
    def has_trained_model(self) -> bool:
        """Check if a model has been trained."""
        return st.session_state.get('model_info') is not None
    
    # Configuration management methods
    def get_app_config(self) -> AppConfig:
        """Get application configuration."""
        return st.session_state.app_config
    
    def get_preprocessing_config(self) -> PreprocessingConfig:
        """Get preprocessing configuration."""
        return st.session_state.preprocessing_config
    
    def update_preprocessing_config(self, **kwargs) -> None:
        """Update preprocessing configuration."""
        config = st.session_state.preprocessing_config
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        st.session_state.preprocessing_config = config
    
    def get_model_config(self) -> ModelConfig:
        """Get model configuration."""
        return st.session_state.model_config
    
    def update_model_config(self, **kwargs) -> None:
        """Update model configuration."""
        config = st.session_state.model_config
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        st.session_state.model_config = config
    
    # Cache management methods
    def cache_feature_importance(self, model_id: str, importance: pd.DataFrame) -> None:
        """Cache feature importance results."""
        st.session_state.feature_importance_cache[model_id] = importance
    
    def get_cached_feature_importance(self, model_id: str) -> Optional[pd.DataFrame]:
        """Get cached feature importance."""
        return st.session_state.feature_importance_cache.get(model_id)
    
    def cache_leaderboard(self, model_id: str, leaderboard: pd.DataFrame) -> None:
        """Cache leaderboard results."""
        st.session_state.leaderboard_cache[model_id] = leaderboard
    
    def get_cached_leaderboard(self, model_id: str) -> Optional[pd.DataFrame]:
        """Get cached leaderboard."""
        return st.session_state.leaderboard_cache.get(model_id)
    
    def cache_evaluation(self, model_id: str, evaluation: Dict[str, Any]) -> None:
        """Cache evaluation results."""
        st.session_state.evaluation_cache[model_id] = evaluation
    
    def get_cached_evaluation(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get cached evaluation."""
        return st.session_state.evaluation_cache.get(model_id)
    
    # Prediction management methods
    def set_last_batch_predictions(self, predictions: pd.DataFrame) -> None:
        """Set last batch predictions."""
        st.session_state.last_batch_predictions = predictions
    
    def get_last_batch_predictions(self) -> Optional[pd.DataFrame]:
        """Get last batch predictions."""
        return st.session_state.get('last_batch_predictions')
    
    # API server management
    def set_api_process(self, pid: int) -> None:
        """Set API server process ID."""
        st.session_state.api_proc = pid
    
    def get_api_process(self) -> Optional[int]:
        """Get API server process ID."""
        return st.session_state.get('api_proc')
    
    def clear_api_process(self) -> None:
        """Clear API server process ID."""
        st.session_state.api_proc = None
    
    # Utility methods
    def clear_all_data(self) -> None:
        """Clear all data from session state."""
        keys_to_clear = [
            'raw_data', 'processed_data', 'data_info', 'processing_steps',
            'image_dir', 'model_info', 'predictor_path', 'predictor_type',
            'last_batch_predictions'
        ]
        
        for key in keys_to_clear:
            if key in st.session_state:
                st.session_state[key] = None
        
        # Clear caches
        st.session_state.feature_importance_cache = {}
        st.session_state.leaderboard_cache = {}
        st.session_state.evaluation_cache = {}
        
        logger.info("All data cleared from session state")
    
    def clear_model_data(self) -> None:
        """Clear only model-related data."""
        keys_to_clear = [
            'model_info', 'predictor_path', 'predictor_type',
            'last_batch_predictions'
        ]
        
        for key in keys_to_clear:
            if key in st.session_state:
                st.session_state[key] = None
        
        # Clear model-related caches
        st.session_state.feature_importance_cache = {}
        st.session_state.leaderboard_cache = {}
        st.session_state.evaluation_cache = {}
        
        logger.info("Model data cleared from session state")
    
    def get_state_summary(self) -> Dict[str, Any]:
        """Get summary of current state."""
        raw_data = self.get_raw_data()
        processed_data = self.get_processed_data()
        model_info = self.get_model_info()
        
        return {
            'has_raw_data': raw_data is not None,
            'raw_data_shape': raw_data.shape if raw_data is not None else None,
            'has_processed_data': processed_data is not None,
            'processed_data_shape': processed_data.shape if processed_data is not None else None,
            'has_model': model_info is not None,
            'model_type': model_info.model_type if model_info else None,
            'processing_steps_count': len(st.session_state.get('processing_steps', [])),
            'has_image_dir': self.get_image_directory() is not None,
            'has_api_process': self.get_api_process() is not None,
            'cache_sizes': {
                'feature_importance': len(st.session_state.get('feature_importance_cache', {})),
                'leaderboard': len(st.session_state.get('leaderboard_cache', {})),
                'evaluation': len(st.session_state.get('evaluation_cache', {}))
            }
        }
    
    def export_state(self) -> Dict[str, Any]:
        """Export current state for backup/restore."""
        # Note: This excludes large DataFrames and non-serializable objects
        exportable_state = {
            'data_info': self.get_data_info(),
            'processing_steps': st.session_state.get('processing_steps', []),
            'model_info': self.get_model_info(),
            'app_config': self.get_app_config(),
            'preprocessing_config': self.get_preprocessing_config(),
            'model_config': self.get_model_config(),
            'image_dir': self.get_image_directory(),
            'export_timestamp': datetime.now().isoformat()
        }
        
        return exportable_state
