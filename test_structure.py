"""
Quick test to verify the improved code structure works.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all main modules can be imported."""
    try:
        # Test core models
        from src.models.config import AppConfig, ModelConfig, PreprocessingConfig
        from src.models.data_models import DataInfo, ModelInfo, PredictionResult
        from src.models.exceptions import MLPlatformError, DataProcessingError
        
        # Test data modules
        from src.data.ingestion import DataIngestionManager
        from src.data.preprocessing import DataPreprocessor
        from src.data.validation import DataValidator
        from src.data.utils import DataUtils
        
        # Test ML modules
        from src.ml.training import ModelTrainer
        from src.ml.evaluation import ModelEvaluator
        from src.ml.prediction import ModelPredictor
        from src.ml.ablation import AblationAnalyzer
        
        # Test UI modules
        from src.ui.state_manager import StateManager
        from src.ui.components import UIComponents
        from src.ui.app import <PERSON>litApp
        from src.ui.pages import DataIngestionPage, ExplorationPage
        
        print("✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of key components."""
    try:
        # Import here to avoid issues with failed imports above
        from src.models.config import AppConfig, ModelConfig, PreprocessingConfig
        from src.data.utils import DataUtils

        # Test configuration
        app_config = AppConfig()
        model_config = ModelConfig()
        preprocessing_config = PreprocessingConfig()

        # Validate configurations
        model_config.validate()
        preprocessing_config.validate()

        # Test data utils
        cudf_available = DataUtils.check_cudf_availability()
        print(f"RAPIDS/cuDF available: {cudf_available}")

        # Test state manager
        # Note: This would normally require Streamlit session state
        # state_manager = StateManager()

        print("✅ Basic functionality tests passed!")
        return True

    except Exception as e:
        print(f"❌ Functionality test error: {e}")
        return False

def test_demo_data_loading():
    """Test demo data loading functionality."""
    try:
        from src.data.ingestion import DataIngestionManager
        
        ingestion_manager = DataIngestionManager()
        
        # Test demo dataset loading
        df, data_info = ingestion_manager.load_demo_dataset("iris")
        
        print(f"✅ Demo data loaded successfully: {df.shape}")
        print(f"   Data info: {data_info.name}, {data_info.memory_usage:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo data loading error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing improved code structure...")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Demo Data Loading", test_demo_data_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        result = test_func()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASSED" if results[i] else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The improved structure is working correctly.")
        print("\n🚀 To run the application:")
        print("   streamlit run app.py")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
